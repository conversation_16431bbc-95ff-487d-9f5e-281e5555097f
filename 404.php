<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package Volkena
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <section class="error-404 not-found">
        <div class="container">
            <div class="error-content">
                <div class="error-visual">
                    <div class="error-number">404</div>
                    <div class="error-icon">🦻</div>
                </div>
                
                <div class="error-text">
                    <h1 class="page-title"><?php _e('Page Not Found', 'volkena'); ?></h1>
                    <p class="error-description">
                        <?php _e('Sorry, the page you are looking for could not be found. It might have been moved, deleted, or you entered the wrong URL.', 'volkena'); ?>
                    </p>
                    
                    <div class="error-actions">
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary">
                            <?php _e('Go to Homepage', 'volkena'); ?>
                        </a>
                        <a href="<?php echo esc_url(get_post_type_archive_link('product')); ?>" class="btn btn-outline">
                            <?php _e('View Products', 'volkena'); ?>
                        </a>
                    </div>
                    
                    <div class="search-section">
                        <h3><?php _e('Search Our Site', 'volkena'); ?></h3>
                        <?php get_search_form(); ?>
                    </div>
                    
                    <div class="helpful-links">
                        <h3><?php _e('Popular Pages', 'volkena'); ?></h3>
                        <ul class="helpful-links-list">
                            <li><a href="<?php echo esc_url(get_post_type_archive_link('product')); ?>"><?php _e('Our Products', 'volkena'); ?></a></li>
                            <li><a href="<?php echo esc_url(get_post_type_archive_link('service')); ?>"><?php _e('Our Services', 'volkena'); ?></a></li>
                            <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('about'))); ?>"><?php _e('About Us', 'volkena'); ?></a></li>
                            <li><a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>"><?php _e('Contact Us', 'volkena'); ?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<style>
.error-404 {
    padding: 100px 0;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.error-content {
    max-width: 600px;
    margin: 0 auto;
}

.error-visual {
    margin-bottom: 3rem;
}

.error-number {
    font-size: 8rem;
    font-weight: 700;
    color: #3498db;
    line-height: 1;
    margin-bottom: 1rem;
}

.error-icon {
    font-size: 4rem;
    opacity: 0.7;
}

.error-text h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.error-description {
    font-size: 1.1rem;
    color: #7f8c8d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.search-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.search-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.helpful-links {
    text-align: left;
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.helpful-links h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    text-align: center;
}

.helpful-links-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.helpful-links-list li {
    text-align: center;
}

.helpful-links-list a {
    display: block;
    padding: 1rem;
    background: #f8f9fa;
    color: #2c3e50;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.helpful-links-list a:hover {
    background: #3498db;
    color: white;
}

@media (max-width: 768px) {
    .error-number {
        font-size: 6rem;
    }
    
    .error-text h1 {
        font-size: 2rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .helpful-links-list {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
