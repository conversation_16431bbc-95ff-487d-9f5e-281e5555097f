# Völkenahearing Excel Import Instructions / এক্সেল ইমপোর্ট নির্দেশাবলী

## English Instructions

### Overview
The Völkenahearing Excel Import system allows you to bulk import hearing aid products from Excel files (.xlsx, .xls) with support for files up to 300MB.

### Column Structure
Your Excel file must have these columns in this exact order:

| Column | Field Name | Description | Required |
|--------|------------|-------------|----------|
| A | No. | Sequential number/ID | Optional |
| B | Model No. | Product model number | **Required** |
| C | Photo | Image filename or URL | Optional |
| D | Packaging | Packaging information | Optional |
| E | Accessories | Included accessories (separated by \|) | Optional |
| F | Description | Product description | Optional |
| G | Specification | Technical specifications (Name: Value format) | Optional |
| H | Measurement | Product dimensions/measurements | Optional |
| I | Price | Product price (numbers only) | Optional |

### Step-by-Step Instructions
1. **Download Template**: Go to Products > Download Template in WordPress admin
2. **Fill Data**: Enter your product information in the template
3. **Save File**: Save as .xlsx or .xls format
4. **Import**: Go to Products > Excel Import and upload your file
5. **Choose Mode**: Select import mode (create new, update existing, or both)
6. **Import**: Click "Import Products" to process

---

## বাংলা নির্দেশাবলী

### সংক্ষিপ্ত বিবরণ
Völkenahearing এক্সেল ইমপোর্ট সিস্টেম আপনাকে এক্সেল ফাইল (.xlsx, .xls) থেকে শ্রবণযন্ত্র পণ্যগুলি বাল্ক ইমপোর্ট করতে দেয়। এটি ৩০০MB পর্যন্ত ফাইল সাপোর্ট করে।

### কলাম কাঠামো
আপনার এক্সেল ফাইলে এই সঠিক ক্রমে নিম্নলিখিত কলামগুলি থাকতে হবে:

| কলাম | ক্ষেত্রের নাম | বিবরণ | প্রয়োজনীয় |
|------|-------------|-------|------------|
| A | নং | ক্রমিক নম্বর/আইডি | ঐচ্ছিক |
| B | মডেল নং | পণ্যের মডেল নম্বর | **প্রয়োজনীয়** |
| C | ছবি | ছবির ফাইলনাম বা URL | ঐচ্ছিক |
| D | প্যাকেজিং | প্যাকেজিং তথ্য | ঐচ্ছিক |
| E | আনুষাঙ্গিক | অন্তর্ভুক্ত আনুষাঙ্গিক (\| দ্বারা পৃথক) | ঐচ্ছিক |
| F | বিবরণ | পণ্যের বিবরণ | ঐচ্ছিক |
| G | স্পেসিফিকেশন | প্রযুক্তিগত স্পেসিফিকেশন (নাম: মান ফরম্যাট) | ঐচ্ছিক |
| H | পরিমাপ | পণ্যের মাত্রা/পরিমাপ | ঐচ্ছিক |
| I | দাম | পণ্যের দাম (শুধুমাত্র সংখ্যা) | ঐচ্ছিক |

### ধাপে ধাপে নির্দেশাবলী
1. **টেমপ্লেট ডাউনলোড**: WordPress অ্যাডমিনে Products > Download Template এ যান
2. **ডেটা পূরণ**: টেমপ্লেটে আপনার পণ্যের তথ্য লিখুন
3. **ফাইল সংরক্ষণ**: .xlsx বা .xls ফরম্যাটে সংরক্ষণ করুন
4. **ইমপোর্ট**: Products > Excel Import এ যান এবং আপনার ফাইল আপলোড করুন
5. **মোড নির্বাচন**: ইমপোর্ট মোড নির্বাচন করুন (নতুন তৈরি, বিদ্যমান আপডেট, বা উভয়)
6. **ইমপোর্ট**: প্রক্রিয়া করতে "Import Products" ক্লিক করুন

### নমুনা ডেটা / Sample Data

```
নং | মডেল নং | ছবি | প্যাকেজিং | আনুষাঙ্গিক | বিবরণ | স্পেসিফিকেশন | পরিমাপ | দাম
1 | VH-ATOM-PRO | atom-pro.jpg | Premium Box | Charging Case|Cleaning Kit | আমাদের সবচেয়ে উন্নত অদৃশ্য শ্রবণযন্ত্র | Frequency: 100Hz-8kHz, Battery: 24h | 15x10x8mm | 299
2 | VH-POWER-BTE | power-bte.jpg | Standard Box | Battery Pack|Ear Hooks | সব ধরনের শ্রবণ ক্ষতির জন্য শক্তিশালী | Frequency: 80Hz-10kHz, Battery: 7d | 45x25x15mm | 199
```

### গুরুত্বপূর্ণ নোট
- **মডেল নং** অবশ্যই প্রয়োজন
- আনুষাঙ্গিক আইটেমগুলি | (পাইপ) চিহ্ন দিয়ে আলাদা করুন
- দাম শুধুমাত্র সংখ্যা হতে হবে (কোন মুদ্রা চিহ্ন নয়)
- সর্বোচ্চ ফাইল সাইজ: ৩০০MB
- সমর্থিত ফরম্যাট: .xlsx, .xls

### সমস্যা সমাধান
- যদি ইমপোর্ট ব্যর্থ হয়, ফাইল ফরম্যাট এবং কলাম ক্রম পরীক্ষা করুন
- নিশ্চিত করুন যে মডেল নং কলাম খালি নেই
- বড় ফাইলের জন্য, ছোট ব্যাচে ভাগ করে ইমপোর্ট করুন

### সহায়তা
আরও সহায়তার জন্য, Völkenahearing ডেভেলপমেন্ট টিমের সাথে যোগাযোগ করুন।

---

## Technical Notes for Developers

### Database Schema
The import system creates the following custom meta fields:
- `_product_model_no` (required, unique identifier)
- `_product_no` (optional sequence number)
- `_product_photo` (image filename/URL)
- `_product_packaging` (packaging information)
- `_product_accessories` (array of accessories)
- `_product_specification` (technical specs)
- `_product_measurement` (dimensions)
- `_product_price` (numeric price)

### Import Modes
1. **Create New Only**: Only creates products that don't exist
2. **Update Existing**: Only updates products that already exist
3. **Create and Update**: Creates new products and updates existing ones

### File Processing
- Uses ZipArchive for XLSX files
- Supports files up to 300MB
- Validates file type and structure
- Provides detailed error reporting
- Logs all import activities

### Error Handling
- Validates required fields
- Checks file format and size
- Provides row-by-row error reporting
- Maintains import history
- Rollback capability for failed imports
