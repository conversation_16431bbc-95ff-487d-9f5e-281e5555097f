# ভোল্কেনা ওয়ার্ডপ্রেস থিম কাস্টমাইজেশন গাইড

এই গাইডটি আপনাকে ভোল্কেনা ওয়ার্ডপ্রেস থিম কাস্টমাইজ করার জন্য সম্পূর্ণ নির্দেশনা প্রদান করবে। এই থিমটি জার্মান হিয়ারিং এইড কোম্পানি ভোল্কেনার জন্য বিশেষভাবে ডিজাইন করা হয়েছে।

## 📋 সূচিপত্র

1. [থিম ব্যাকআপ করা](#থিম-ব্যাকআপ-করা)
2. [রং, ফন্ট এবং স্টাইলিং পরিবর্তন](#রং-ফন্ট-এবং-স্টাইলিং-পরিবর্তন)
3. [হোমপেজ কন্টেন্ট এবং লেআউট কাস্টমাইজ](#হোমপেজ-কন্টেন্ট-এবং-লেআউট-কাস্টমাইজ)
4. [প্রোডাক্ট এবং সার্ভিস তথ্য যোগ/পরিবর্তন](#প্রোডাক্ট-এবং-সার্ভিস-তথ্য-যোগপরিবর্তন)
5. [কোম্পানির তথ্য এবং যোগাযোগ পরিবর্তন](#কোম্পানির-তথ্য-এবং-যোগাযোগ-পরিবর্তন)
6. [হেডার, ফুটার এবং নেভিগেশন মেনু](#হেডার-ফুটার-এবং-নেভিগেশন-মেনু)
7. [বহুভাষিক সেটিংস এবং অনুবাদ](#বহুভাষিক-সেটিংস-এবং-অনুবাদ)
8. [কাস্টম ফাংশনালিটি যোগ করা](#কাস্টম-ফাংশনালিটি-যোগ-করা)
9. [সাধারণ কাস্টমাইজেশন কাজের ধাপ](#সাধারণ-কাস্টমাইজেশন-কাজের-ধাপ)
10. [থিম রক্ষণাবেক্ষণের সেরা অনুশীলন](#থিম-রক্ষণাবেক্ষণের-সেরা-অনুশীলন)

---

## 🔒 থিম ব্যাকআপ করা

কোনো পরিবর্তন করার আগে অবশ্যই থিমের ব্যাকআপ নিন:

### ম্যানুয়াল ব্যাকআপ:
1. **FTP/cPanel** দিয়ে আপনার সার্ভারে লগইন করুন
2. `/wp-content/themes/volkena/` ফোল্ডারটি কপি করুন
3. এটি `volkena-backup-[তারিখ]` নামে সেভ করুন

### প্লাগইন দিয়ে ব্যাকআপ:
- **UpdraftPlus** বা **BackWPup** প্লাগইন ব্যবহার করুন
- সম্পূর্ণ সাইটের ব্যাকআপ নিন

### চাইল্ড থিম তৈরি (সুপারিশকৃত):
```php
// wp-content/themes/volkena-child/style.css
/*
Theme Name: Volkena Child
Template: volkena
Version: 1.0.0
*/

@import url("../volkena/style.css");

/* আপনার কাস্টম CSS এখানে লিখুন */
```

---

## 🎨 রং, ফন্ট এবং স্টাইলিং পরিবর্তন

### প্রাথমিক রং পরিবর্তন:

**ফাইল:** `style.css` (লাইন ১-৫০)

```css
:root {
    --primary-color: #3498db;    /* নীল রং */
    --secondary-color: #27ae60;  /* সবুজ রং */
    --text-color: #2c3e50;       /* টেক্সট রং */
    --background-color: #ffffff; /* ব্যাকগ্রাউন্ড রং */
}
```

### কাস্টম রং যোগ করা:
```css
/* আপনার পছন্দের রং */
:root {
    --primary-color: #e74c3c;    /* লাল রং */
    --secondary-color: #f39c12;  /* কমলা রং */
    --accent-color: #9b59b6;     /* বেগুনি রং */
}
```

### ফন্ট পরিবর্তন:

**ফাইল:** `functions.php` (লাইন ৫০-৭০)

```php
function volkena_scripts() {
    // Google Fonts যোগ করুন
    wp_enqueue_style('volkena-fonts', 
        'https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap'
    );
}
```

**CSS-তে ফন্ট প্রয়োগ:**
```css
body {
    font-family: 'Roboto', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Roboto', sans-serif;
    font-weight: 700;
}
```

### বাটন স্টাইল কাস্টমাইজ:
```css
.btn-primary {
    background: linear-gradient(135deg, #your-color1, #your-color2);
    border-radius: 25px; /* গোলাকার বাটন */
    padding: 15px 30px;
    font-weight: 600;
}
```

---

## 🏠 হোমপেজ কন্টেন্ট এবং লেআউট কাস্টমাইজ

### হিরো সেকশন পরিবর্তন:

**ফাইল:** `front-page.php` (লাইন ১৫-৪০)

```php
<section class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">
            <?php echo get_theme_mod('hero_title', 'আপনার নতুন টাইটেল'); ?>
        </h1>
        <p class="hero-subtitle">
            <?php echo get_theme_mod('hero_subtitle', 'আপনার নতুন সাবটাইটেল'); ?>
        </p>
    </div>
</section>
```

### কাস্টমাইজারে হিরো সেকশন যোগ:

**ফাইল:** `functions.php`

```php
function volkena_customize_register($wp_customize) {
    // হিরো সেকশন
    $wp_customize->add_section('hero_section', array(
        'title' => 'হিরো সেকশন',
        'priority' => 30,
    ));
    
    // হিরো টাইটেল
    $wp_customize->add_setting('hero_title');
    $wp_customize->add_control('hero_title', array(
        'label' => 'হিরো টাইটেল',
        'section' => 'hero_section',
        'type' => 'text',
    ));
}
add_action('customize_register', 'volkena_customize_register');
```

### সেকশন যোগ/সরানো:

**নতুন সেকশন যোগ করা:**
```php
<!-- front-page.php এ যোগ করুন -->
<section class="custom-section">
    <div class="container">
        <h2>আপনার কাস্টম সেকশন</h2>
        <p>এখানে আপনার কন্টেন্ট লিখুন</p>
    </div>
</section>
```

---

## 🛍️ প্রোডাক্ট এবং সার্ভিস তথ্য যোগ/পরিবর্তন

### নতুন প্রোডাক্ট যোগ করা:

1. **WordPress Admin** → **Products** → **Add New**
2. প্রোডাক্টের তথ্য পূরণ করুন:
   - **Title:** প্রোডাক্টের নাম
   - **Content:** বিস্তারিত বর্ণনা
   - **Product Type:** CIC, BTE, বা ITE
   - **Price:** দাম
   - **Features:** বৈশিষ্ট্যসমূহ

### প্রোডাক্ট মেটা ফিল্ড কাস্টমাইজ:

**ফাইল:** `functions.php` (লাইন ১৫০-২০০)

```php
function volkena_product_meta_boxes() {
    add_meta_box(
        'product_details',
        'প্রোডাক্টের বিস্তারিত',
        'volkena_product_details_callback',
        'product'
    );
}

function volkena_product_details_callback($post) {
    $price = get_post_meta($post->ID, '_product_price', true);
    $type = get_post_meta($post->ID, '_product_type', true);
    
    echo '<label>দাম:</label>';
    echo '<input type="text" name="product_price" value="' . $price . '">';
    
    echo '<label>ধরন:</label>';
    echo '<select name="product_type">';
    echo '<option value="CIC"' . selected($type, 'CIC', false) . '>CIC</option>';
    echo '<option value="BTE"' . selected($type, 'BTE', false) . '>BTE</option>';
    echo '<option value="ITE"' . selected($type, 'ITE', false) . '>ITE</option>';
    echo '</select>';
}
```

### প্রোডাক্ট লেআউট পরিবর্তন:

**ফাইল:** `archive-product.php`

```php
<div class="product-card">
    <div class="product-image">
        <?php the_post_thumbnail('medium'); ?>
    </div>
    <div class="product-content">
        <h3><?php the_title(); ?></h3>
        <p class="product-price">
            <?php echo get_post_meta(get_the_ID(), '_product_price', true); ?>
        </p>
        <a href="<?php the_permalink(); ?>" class="btn btn-primary">
            বিস্তারিত দেখুন
        </a>
    </div>
</div>
```

---

## 📞 কোম্পানির তথ্য এবং যোগাযোগ পরিবর্তন

### WhatsApp নম্বর পরিবর্তন:

**WordPress Admin** → **Appearance** → **Customize** → **Contact Information**

অথবা **functions.php** এ:

```php
function volkena_customize_register($wp_customize) {
    // যোগাযোগ সেকশন
    $wp_customize->add_section('contact_info', array(
        'title' => 'যোগাযোগের তথ্য',
        'priority' => 40,
    ));
    
    // WhatsApp নম্বর
    $wp_customize->add_setting('whatsapp_number');
    $wp_customize->add_control('whatsapp_number', array(
        'label' => 'WhatsApp নম্বর',
        'section' => 'contact_info',
        'type' => 'text',
    ));
}
```

### যোগাযোগ পৃষ্ঠা কাস্টমাইজ:

**ফাইল:** `page-contact.php`

```php
<div class="contact-info">
    <h3>আমাদের সাথে যোগাযোগ করুন</h3>
    <p><strong>ফোন:</strong> +৮৮০১৭১২৩৪৫৬৭৮</p>
    <p><strong>ইমেইল:</strong> <EMAIL></p>
    <p><strong>ঠিকানা:</strong> ঢাকা, বাংলাদেশ</p>
</div>
```

### কোম্পানির লোগো পরিবর্তন:

**WordPress Admin** → **Appearance** → **Customize** → **Site Identity** → **Logo**

---

## 🧭 হেডার, ফুটার এবং নেভিগেশন মেনু

### হেডার কাস্টমাইজ:

**ফাইল:** `header.php`

```php
<header class="site-header">
    <div class="container">
        <div class="header-content">
            <!-- লোগো -->
            <div class="site-branding">
                <?php if (has_custom_logo()) : ?>
                    <?php the_custom_logo(); ?>
                <?php else : ?>
                    <h1><a href="<?php echo home_url(); ?>">ভোল্কেনা</a></h1>
                <?php endif; ?>
            </div>
            
            <!-- নেভিগেশন মেনু -->
            <nav class="main-navigation">
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'menu_class' => 'nav-menu',
                ));
                ?>
            </nav>
        </div>
    </div>
</header>
```

### মেনু তৈরি এবং সেট করা:

1. **WordPress Admin** → **Appearance** → **Menus**
2. **Create a new menu** ক্লিক করুন
3. মেনুর নাম দিন (যেমন: "প্রধান মেনু")
4. পৃষ্ঠা/পোস্ট যোগ করুন
5. **Menu Settings** এ **Primary Menu** চেক করুন
6. **Save Menu** ক্লিক করুন

### ফুটার কাস্টমাইজ:

**ফাইল:** `footer.php`

```php
<footer class="site-footer">
    <div class="container">
        <div class="footer-content">
            <div class="footer-section">
                <h4>ভোল্কেনা</h4>
                <p>জার্মান প্রযুক্তির হিয়ারিং এইড</p>
            </div>
            
            <div class="footer-section">
                <h4>দ্রুত লিংক</h4>
                <ul>
                    <li><a href="/products">প্রোডাক্ট</a></li>
                    <li><a href="/services">সেবা</a></li>
                    <li><a href="/about">আমাদের সম্পর্কে</a></li>
                    <li><a href="/contact">যোগাযোগ</a></li>
                </ul>
            </div>
        </div>
    </div>
</footer>
```

---

## 🌐 বহুভাষিক সেটিংস এবং অনুবাদ

### বাংলা অনুবাদ ফাইল তৈরি:

**ফাইল:** `languages/bn_BD.po`

```po
msgid "Our Products"
msgstr "আমাদের প্রোডাক্ট"

msgid "Contact Us"
msgstr "যোগাযোগ করুন"

msgid "Learn More"
msgstr "আরও জানুন"
```

### অনুবাদ যোগ করার পদ্ধতি:

1. **Poedit** সফটওয়্যার ডাউনলোড করুন
2. `languages/volkena.pot` ফাইল খুলুন
3. বাংলা অনুবাদ যোগ করুন
4. `bn_BD.po` এবং `bn_BD.mo` ফাইল সেভ করুন

### ভাষা স্যুইচার কাস্টমাইজ:

**ফাইল:** `header.php`

```php
<div class="language-switcher">
    <select id="language-select">
        <option value="en">English</option>
        <option value="de">Deutsch</option>
        <option value="bn">বাংলা</option>
    </select>
</div>
```

### Polylang প্লাগইন সেটআপ:

1. **Polylang** প্লাগইন ইনস্টল করুন
2. **Languages** → **Settings** এ যান
3. ভাষা যোগ করুন (বাংলা, ইংরেজি, জার্মান)
4. প্রতিটি পৃষ্ঠার জন্য অনুবাদ তৈরি করুন

---

## ⚙️ কাস্টম ফাংশনালিটি যোগ করা

### নতুন শর্টকোড তৈরি:

**ফাইল:** `functions.php`

```php
// প্রোডাক্ট গ্রিড শর্টকোড
function volkena_product_grid_shortcode($atts) {
    $atts = shortcode_atts(array(
        'count' => 6,
        'type' => 'all'
    ), $atts);
    
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => $atts['count']
    );
    
    if ($atts['type'] !== 'all') {
        $args['meta_query'] = array(
            array(
                'key' => '_product_type',
                'value' => $atts['type']
            )
        );
    }
    
    $products = new WP_Query($args);
    
    ob_start();
    if ($products->have_posts()) {
        echo '<div class="product-grid">';
        while ($products->have_posts()) {
            $products->the_post();
            // প্রোডাক্ট কার্ড HTML
        }
        echo '</div>';
    }
    wp_reset_postdata();
    
    return ob_get_clean();
}
add_shortcode('product_grid', 'volkena_product_grid_shortcode');
```

### কাস্টম CSS ক্লাস যোগ:

```php
function volkena_body_classes($classes) {
    if (is_front_page()) {
        $classes[] = 'homepage';
    }
    if (is_singular('product')) {
        $classes[] = 'single-product-page';
    }
    return $classes;
}
add_filter('body_class', 'volkena_body_classes');
```

### AJAX ফাংশনালিটি যোগ:

```php
function volkena_ajax_contact_form() {
    // নিরাপত্তা যাচাই
    if (!wp_verify_nonce($_POST['nonce'], 'volkena_nonce')) {
        wp_die('নিরাপত্তা যাচাই ব্যর্থ');
    }
    
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // ইমেইল পাঠানো
    $to = get_option('admin_email');
    $subject = 'নতুন যোগাযোগ ফর্ম জমা';
    $body = "নাম: $name\nইমেইল: $email\nবার্তা: $message";
    
    if (wp_mail($to, $subject, $body)) {
        wp_send_json_success('আপনার বার্তা সফলভাবে পাঠানো হয়েছে!');
    } else {
        wp_send_json_error('বার্তা পাঠাতে সমস্যা হয়েছে।');
    }
}
add_action('wp_ajax_volkena_contact_form', 'volkena_ajax_contact_form');
add_action('wp_ajax_nopriv_volkena_contact_form', 'volkena_ajax_contact_form');
```

---

## 📝 সাধারণ কাস্টমাইজেশন কাজের ধাপ

### ১. হোমপেজে নতুন সেকশন যোগ:

**ধাপ ১:** `front-page.php` ফাইল খুলুন
**ধাপ ২:** যেখানে সেকশন যোগ করতে চান সেখানে কোড লিখুন:

```php
<section class="আপনার-সেকশন-নাম">
    <div class="container">
        <h2>সেকশনের শিরোনাম</h2>
        <p>সেকশনের বিষয়বস্তু</p>
    </div>
</section>
```

**ধাপ ৩:** `style.css` এ স্টাইল যোগ করুন:

```css
.আপনার-সেকশন-নাম {
    padding: 80px 0;
    background: #f8f9fa;
}

.আপনার-সেকশন-নাম h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2c3e50;
}
```

### ২. কাস্টম পোস্ট টাইপ তৈরি:

```php
function create_testimonial_post_type() {
    register_post_type('testimonial', array(
        'labels' => array(
            'name' => 'গ্রাহক মতামত',
            'singular_name' => 'মতামত'
        ),
        'public' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-format-quote'
    ));
}
add_action('init', 'create_testimonial_post_type');
```

### ৩. কাস্টম ফিল্ড যোগ:

```php
function add_testimonial_meta_box() {
    add_meta_box(
        'testimonial_details',
        'গ্রাহকের তথ্য',
        'testimonial_meta_callback',
        'testimonial'
    );
}

function testimonial_meta_callback($post) {
    $rating = get_post_meta($post->ID, '_rating', true);
    echo '<label>রেটিং (১-৫):</label>';
    echo '<input type="number" name="rating" value="' . $rating . '" min="1" max="5">';
}
```

---

## 🛡️ থিম রক্ষণাবেক্ষণের সেরা অনুশীলন

### ১. নিয়মিত ব্যাকআপ:
- সাপ্তাহিক ব্যাকআপ নিন
- পরিবর্তনের আগে অবশ্যই ব্যাকআপ নিন
- ব্যাকআপ ফাইল আলাদা জায়গায় রাখুন

### ২. কোড ডকুমেন্টেশন:
```php
/**
 * কাস্টম ফাংশনের বর্ণনা
 * @param string $parameter প্যারামিটারের বর্ণনা
 * @return string রিটার্ন ভ্যালুর বর্ণনা
 */
function your_custom_function($parameter) {
    // আপনার কোড
}
```

### ৩. টেস্টিং পরিবেশ:
- স্টেজিং সাইট ব্যবহার করুন
- পরিবর্তন প্রথমে টেস্ট করুন
- সব ব্রাউজারে চেক করুন

### ৪. পারফরমেন্স অপটিমাইজেশন:
```php
// CSS/JS মিনিফাই করুন
function volkena_enqueue_minified_assets() {
    if (!is_admin()) {
        wp_enqueue_style('volkena-style', get_template_directory_uri() . '/style.min.css');
    }
}
```

### ৫. নিরাপত্তা:
```php
// ইনপুট স্যানিটাইজ করুন
$user_input = sanitize_text_field($_POST['input']);

// নন্স ব্যবহার করুন
wp_nonce_field('volkena_action', 'volkena_nonce');
```

### ৬. আপডেট ট্র্যাকিং:
```php
// ভার্সন নম্বর ট্র্যাক করুন
define('VOLKENA_VERSION', '1.0.1');

// চেঞ্জলগ রাখুন
/*
Version 1.0.1:
- বাংলা ভাষা সাপোর্ট যোগ
- মোবাইল রেসপন্সিভনেস উন্নত
*/
```

---

## 🚨 সাধারণ সমস্যা এবং সমাধান

### সমস্যা ১: CSS পরিবর্তন দেখা যাচ্ছে না
**সমাধান:**
- ব্রাউজার ক্যাশ ক্লিয়ার করুন
- ক্যাশিং প্লাগইন ক্লিয়ার করুন
- Hard refresh করুন (Ctrl+F5)

### সমস্যা ২: ফাংশন এরর
**সমাধান:**
```php
// এরর চেকিং যোগ করুন
if (function_exists('your_function')) {
    your_function();
}
```

### সমস্যা ৩: মোবাইলে সমস্যা
**সমাধান:**
```css
/* মোবাইল-ফার্স্ট অ্যাপ্রোচ */
@media (max-width: 768px) {
    .your-element {
        font-size: 14px;
        padding: 10px;
    }
}
```

---

## 📞 সাহায্য এবং সাপোর্ট

যদি কোনো সমস্যার সম্মুখীন হন:

1. **ডকুমেন্টেশন** আবার পড়ুন
2. **ব্যাকআপ** থেকে রিস্টোর করুন
3. **WordPress কমিউনিটি** ফোরামে প্রশ্ন করুন
4. **ডেভেলপার সাপোর্ট** যোগাযোগ করুন

---

**মনে রাখবেন:** সব পরিবর্তন ধীরে ধীরে করুন এবং প্রতিটি পরিবর্তনের পর টেস্ট করুন। এতে করে সমস্যা হলে সহজেই সমাধান করতে পারবেন।

**শুভকামনা!** আপনার ভোল্কেনা ওয়েবসাইট কাস্টমাইজেশনে সফল হোন! 🎉

---

## 🔧 অতিরিক্ত কাস্টমাইজেশন টিপস

### কাস্টম CSS ক্লাস ব্যবহার:

```css
/* বিশেষ বাটন স্টাইল */
.btn-bangla {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-family: 'SolaimanLipi', Arial, sans-serif;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-bangla:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

/* বাংলা টেক্সটের জন্য বিশেষ স্টাইল */
.bangla-text {
    font-family: 'SolaimanLipi', 'Kalpurush', Arial, sans-serif;
    line-height: 1.8;
    font-size: 16px;
}

/* প্রোডাক্ট কার্ডে বাংলা দাম */
.price-bangla {
    font-family: 'SolaimanLipi', Arial, sans-serif;
    color: #27ae60;
    font-weight: 700;
    font-size: 18px;
}

.price-bangla::before {
    content: "৳ ";
}
```

### বাংলা ফন্ট যোগ করা:

**ফাইল:** `functions.php`

```php
function volkena_bangla_fonts() {
    // বাংলা ফন্ট যোগ করুন
    wp_enqueue_style('bangla-fonts',
        'https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap'
    );

    // অথবা লোকাল ফন্ট ব্যবহার করুন
    wp_enqueue_style('solaimanlipi',
        get_template_directory_uri() . '/assets/fonts/solaimanlipi.css'
    );
}
add_action('wp_enqueue_scripts', 'volkena_bangla_fonts');
```

### মোবাইল মেনুতে বাংলা সাপোর্ট:

```css
.mobile-menu {
    font-family: 'Hind Siliguri', Arial, sans-serif;
}

.mobile-menu a {
    padding: 15px 20px;
    font-size: 16px;
    line-height: 1.6;
}

/* বাংলা টেক্সটের জন্য অতিরিক্ত স্পেসিং */
.mobile-menu .bangla-menu-item {
    padding: 18px 20px;
}
```

### কাস্টম শর্টকোড (বাংলা):

```php
// বাংলা তারিখ শর্টকোড
function bangla_date_shortcode($atts) {
    $atts = shortcode_atts(array(
        'format' => 'full'
    ), $atts);

    $bangla_months = array(
        'January' => 'জানুয়ারি', 'February' => 'ফেব্রুয়ারি',
        'March' => 'মার্চ', 'April' => 'এপ্রিল',
        'May' => 'মে', 'June' => 'জুন',
        'July' => 'জুলাই', 'August' => 'আগস্ট',
        'September' => 'সেপ্টেম্বর', 'October' => 'অক্টোবর',
        'November' => 'নভেম্বর', 'December' => 'ডিসেম্বর'
    );

    $english_date = date('F j, Y');
    $bangla_date = str_replace(array_keys($bangla_months), array_values($bangla_months), $english_date);

    return $bangla_date;
}
add_shortcode('bangla_date', 'bangla_date_shortcode');

// ব্যবহার: [bangla_date]
```

### প্রোডাক্ট ফিল্টার (বাংলা):

```php
function volkena_product_filter_bangla() {
    $types = array(
        'all' => 'সব ধরনের',
        'CIC' => 'সিআইসি (কানের ভিতরে)',
        'BTE' => 'বিটিই (কানের পিছনে)',
        'ITE' => 'আইটিই (কানের মধ্যে)'
    );

    echo '<div class="product-filter-bangla">';
    echo '<h3>প্রোডাক্ট ফিল্টার:</h3>';
    foreach ($types as $key => $label) {
        echo '<button class="filter-btn" data-filter="' . $key . '">' . $label . '</button>';
    }
    echo '</div>';
}
```

### বাংলা সার্চ ফাংশনালিটি:

```php
function volkena_bangla_search_keywords($search, $wp_query) {
    if (!is_admin() && $wp_query->is_main_query() && $wp_query->is_search()) {
        $search_term = $wp_query->get('s');

        // বাংলা কীওয়ার্ড ম্যাপিং
        $bangla_keywords = array(
            'হিয়ারিং এইড' => 'hearing aid',
            'কানের যন্ত্র' => 'hearing device',
            'শ্রবণ যন্ত্র' => 'hearing aid',
            'সেবা' => 'service',
            'মেরামত' => 'repair',
            'যোগাযোগ' => 'contact'
        );

        if (isset($bangla_keywords[$search_term])) {
            $wp_query->set('s', $bangla_keywords[$search_term]);
        }
    }

    return $search;
}
add_filter('posts_search', 'volkena_bangla_search_keywords', 10, 2);
```

### কাস্টম উইজেট (বাংলা):

```php
class Volkena_Bangla_Contact_Widget extends WP_Widget {

    function __construct() {
        parent::__construct(
            'volkena_bangla_contact',
            'ভোল্কেনা যোগাযোগ',
            array('description' => 'যোগাযোগের তথ্য প্রদর্শন করে')
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . $instance['title'] . $args['after_title'];
        }

        echo '<div class="bangla-contact-widget">';
        echo '<p><strong>ফোন:</strong> ' . $instance['phone'] . '</p>';
        echo '<p><strong>ইমেইল:</strong> ' . $instance['email'] . '</p>';
        echo '<p><strong>ঠিকানা:</strong> ' . $instance['address'] . '</p>';
        echo '</div>';

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'যোগাযোগ করুন';
        $phone = !empty($instance['phone']) ? $instance['phone'] : '';
        $email = !empty($instance['email']) ? $instance['email'] : '';
        $address = !empty($instance['address']) ? $instance['address'] : '';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">শিরোনাম:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>"
                   name="<?php echo $this->get_field_name('title'); ?>" type="text"
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('phone'); ?>">ফোন:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('phone'); ?>"
                   name="<?php echo $this->get_field_name('phone'); ?>" type="text"
                   value="<?php echo esc_attr($phone); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('email'); ?>">ইমেইল:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('email'); ?>"
                   name="<?php echo $this->get_field_name('email'); ?>" type="email"
                   value="<?php echo esc_attr($email); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('address'); ?>">ঠিকানা:</label>
            <textarea class="widefat" id="<?php echo $this->get_field_id('address'); ?>"
                      name="<?php echo $this->get_field_name('address'); ?>"><?php echo esc_attr($address); ?></textarea>
        </p>
        <?php
    }
}

function register_volkena_bangla_widgets() {
    register_widget('Volkena_Bangla_Contact_Widget');
}
add_action('widgets_init', 'register_volkena_bangla_widgets');
```

### বাংলা ইমেইল টেমপ্লেট:

```php
function volkena_bangla_email_template($message, $subject, $email) {
    $bangla_template = '
    <div style="font-family: SolaimanLipi, Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <header style="background: #3498db; color: white; padding: 20px; text-align: center;">
            <h1>ভোল্কেনা</h1>
            <p>জার্মান প্রযুক্তির হিয়ারিং এইড</p>
        </header>

        <main style="padding: 30px; background: white;">
            <h2 style="color: #2c3e50;">নতুন যোগাযোগ বার্তা</h2>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <p><strong>বিষয়:</strong> ' . $subject . '</p>
                <p><strong>ইমেইল:</strong> ' . $email . '</p>
                <p><strong>বার্তা:</strong></p>
                <p style="line-height: 1.6;">' . nl2br($message) . '</p>
            </div>

            <p style="color: #7f8c8d; font-size: 14px;">
                এই বার্তাটি আপনার ওয়েবসাইটের যোগাযোগ ফর্ম থেকে পাঠানো হয়েছে।
            </p>
        </main>

        <footer style="background: #2c3e50; color: white; padding: 20px; text-align: center;">
            <p>&copy; ২০২৪ ভোল্কেনা। সর্বস্বত্ব সংরক্ষিত।</p>
        </footer>
    </div>';

    return $bangla_template;
}
```

### SEO অপটিমাইজেশন (বাংলা):

```php
function volkena_bangla_seo_meta() {
    if (is_singular('product')) {
        $product_type = get_post_meta(get_the_ID(), '_product_type', true);
        $bangla_types = array(
            'CIC' => 'সিআইসি হিয়ারিং এইড',
            'BTE' => 'বিটিই হিয়ারিং এইড',
            'ITE' => 'আইটিই হিয়ারিং এইড'
        );

        if (isset($bangla_types[$product_type])) {
            echo '<meta name="description" content="' . get_the_title() . ' - ' . $bangla_types[$product_type] . ' | ভোল্কেনা জার্মান প্রযুক্তির হিয়ারিং এইড">';
            echo '<meta name="keywords" content="হিয়ারিং এইড, ' . $bangla_types[$product_type] . ', কানের যন্ত্র, ভোল্কেনা">';
        }
    }
}
add_action('wp_head', 'volkena_bangla_seo_meta');
```

### পারফরমেন্স অপটিমাইজেশন:

```php
// বাংলা ফন্ট প্রিলোড
function volkena_preload_bangla_fonts() {
    echo '<link rel="preload" href="' . get_template_directory_uri() . '/assets/fonts/SolaimanLipi.woff2" as="font" type="font/woff2" crossorigin>';
}
add_action('wp_head', 'volkena_preload_bangla_fonts');

// অপ্রয়োজনীয় স্ক্রিপ্ট সরানো
function volkena_remove_unnecessary_scripts() {
    if (!is_admin()) {
        wp_deregister_script('wp-embed');
    }
}
add_action('wp_footer', 'volkena_remove_unnecessary_scripts');
```

### মোবাইল অপটিমাইজেশন:

```css
/* বাংলা টেক্সটের জন্য মোবাইল স্টাইল */
@media (max-width: 768px) {
    .bangla-text {
        font-size: 14px;
        line-height: 1.7;
    }

    .bangla-heading {
        font-size: 20px;
        line-height: 1.4;
        margin-bottom: 15px;
    }

    .product-title-bangla {
        font-size: 16px;
        line-height: 1.5;
    }

    .price-bangla {
        font-size: 16px;
    }
}

/* টাচ-ফ্রেন্ডলি বাটন */
@media (max-width: 768px) {
    .btn-bangla {
        padding: 15px 25px;
        font-size: 16px;
        min-height: 48px;
        display: block;
        text-align: center;
        margin-bottom: 10px;
    }
}
```

---

## 📱 মোবাইল অ্যাপ ইন্টিগ্রেশন

### PWA (Progressive Web App) সেটআপ:

**ফাইল:** `manifest.json`

```json
{
    "name": "ভোল্কেনা - হিয়ারিং এইড",
    "short_name": "ভোল্কেনা",
    "description": "জার্মান প্রযুক্তির হিয়ারিং এইড",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#ffffff",
    "theme_color": "#3498db",
    "lang": "bn",
    "icons": [
        {
            "src": "/assets/images/icon-192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "/assets/images/icon-512.png",
            "sizes": "512x512",
            "type": "image/png"
        }
    ]
}
```

### সার্ভিস ওয়ার্কার (বাংলা):

**ফাইল:** `sw.js`

```javascript
const CACHE_NAME = 'volkena-v1';
const urlsToCache = [
    '/',
    '/style.css',
    '/assets/js/main.js',
    '/assets/fonts/SolaimanLipi.woff2'
];

// ক্যাশ ইনস্টল
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('ক্যাশ খোলা হয়েছে');
                return cache.addAll(urlsToCache);
            })
    );
});

// ক্যাশ থেকে ফাইল পরিবেশন
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            }
        )
    );
});
```

---

## 🎯 সম্পূর্ণ কাস্টমাইজেশন চেকলিস্ট

### ✅ প্রাথমিক সেটআপ:
- [ ] থিম ব্যাকআপ নেওয়া হয়েছে
- [ ] চাইল্ড থিম তৈরি করা হয়েছে
- [ ] প্রয়োজনীয় প্লাগইন ইনস্টল করা হয়েছে

### ✅ ডিজাইন কাস্টমাইজেশন:
- [ ] কোম্পানির রং প্রয়োগ করা হয়েছে
- [ ] বাংলা ফন্ট যোগ করা হয়েছে
- [ ] লোগো আপলোড করা হয়েছে
- [ ] ফেভিকন সেট করা হয়েছে

### ✅ কন্টেন্ট সেটআপ:
- [ ] হোমপেজ কন্টেন্ট আপডেট করা হয়েছে
- [ ] প্রোডাক্ট যোগ করা হয়েছে
- [ ] সার্ভিস পেজ তৈরি করা হয়েছে
- [ ] যোগাযোগের তথ্য আপডেট করা হয়েছে

### ✅ ফাংশনালিটি:
- [ ] যোগাযোগ ফর্ম টেস্ট করা হয়েছে
- [ ] WhatsApp ইন্টিগ্রেশন কাজ করছে
- [ ] প্রোডাক্ট ফিল্টার কাজ করছে
- [ ] সার্চ ফাংশন কাজ করছে

### ✅ অপটিমাইজেশন:
- [ ] মোবাইল রেসপন্সিভনেস চেক করা হয়েছে
- [ ] পেজ স্পিড টেস্ট করা হয়েছে
- [ ] SEO সেটিংস কনফিগার করা হয়েছে
- [ ] অ্যাক্সেসিবিলিটি চেক করা হয়েছে

### ✅ টেস্টিং:
- [ ] সব ব্রাউজারে টেস্ট করা হয়েছে
- [ ] মোবাইল ডিভাইসে টেস্ট করা হয়েছে
- [ ] ফর্ম সাবমিশন টেস্ট করা হয়েছে
- [ ] লিংক এবং নেভিগেশন চেক করা হয়েছে

---

**এই গাইড অনুসরণ করে আপনি সফলভাবে ভোল্কেনা থিম কাস্টমাইজ করতে পারবেন। যেকোনো সমস্যার জন্য ডকুমেন্টেশন পুনরায় পড়ুন এবং ধাপে ধাপে এগিয়ে যান।**

**সফল হোন! 🌟**
