# <PERSON>ölkena WordPress Theme

A modern, professional WordPress theme designed specifically for Völkena, a German hearing aid company. This theme combines German precision design with advanced functionality to showcase hearing aid products and services.

## Features

### Design & Aesthetics
- **Clean, minimal design** reflecting German precision and premium healthcare technology
- **Professional color scheme** with white backgrounds and blue/green accents
- **Modern typography** using Inter font family for excellent readability
- **Responsive design** that works perfectly on all devices
- **Accessibility-focused** with proper ARIA labels and keyboard navigation

### Multi-language Support
- **German primary language** with English translation support
- **Language switcher** in header navigation
- **Translation-ready** with .pot file included
- **German translation file** (de_DE.po) provided

### Product Showcase
- **Custom product post type** for hearing aids
- **Product filtering and sorting** by type (CIC, BTE, ITE)
- **Product comparison functionality** 
- **Detailed product pages** with specifications and features
- **Product grid layouts** with hover effects

### Services Management
- **Custom service post type** for hearing care services
- **Service archive pages** with professional layouts
- **Service detail pages** with comprehensive information
- **Booking integration ready**

### Contact & Communication
- **Professional contact page** with form validation
- **WhatsApp integration** for instant customer support
- **Contact form with AJAX** submission
- **Interactive map placeholder** (ready for Google Maps integration)
- **Multiple contact methods** display

### Blog & Content
- **Educational blog functionality** for hearing health content
- **Related articles system**
- **Author bio sections**
- **Newsletter signup integration**
- **SEO-optimized structure**

### Technical Features
- **Custom post types** for Products and Services
- **Custom meta boxes** for product details
- **AJAX-powered forms** with validation
- **Lazy loading images** for performance
- **Progressive Web App** ready with service worker support
- **Cookie consent** for GDPR compliance
- **Analytics tracking** integration ready

## Installation

1. **Upload the theme** to your WordPress `/wp-content/themes/` directory
2. **Activate the theme** in WordPress Admin > Appearance > Themes
3. **Configure the theme** in Customizer:
   - Set hero title and subtitle
   - Add WhatsApp number
   - Configure contact information
4. **Create essential pages**:
   - About Us (slug: `about`)
   - Contact (slug: `contact`)
5. **Set up navigation** in Appearance > Menus
6. **Add products and services** using the custom post types

## Customization

### Theme Customizer Options
- **Hero Section**: Title and subtitle text
- **Contact Information**: WhatsApp number and contact details
- **Colors**: Primary and secondary color schemes
- **Typography**: Font selections and sizes

### Custom Post Types
- **Products**: Hearing aids with type, price, features, and specifications
- **Services**: Hearing care services with descriptions and features

### Widget Areas
- **Sidebar**: Main sidebar for blog pages
- **Footer 1-4**: Four footer widget areas for comprehensive footer content

## File Structure

```
volkena/
├── style.css                 # Main stylesheet with theme information
├── functions.php             # Theme functions and features
├── index.php                 # Main template file
├── front-page.php           # Homepage template
├── header.php               # Header template
├── footer.php               # Footer template
├── single.php               # Single post template
├── single-product.php       # Single product template
├── archive-product.php      # Product archive template
├── archive-service.php      # Service archive template
├── page-about.php           # About page template
├── page-contact.php         # Contact page template
├── search.php               # Search results template
├── searchform.php           # Search form template
├── 404.php                  # 404 error page template
├── assets/
│   └── js/
│       └── main.js          # Main JavaScript file
└── languages/
    ├── volkena.pot          # Translation template
    └── de_DE.po             # German translation
```

## Browser Support

- **Modern browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Mobile browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **Accessibility**: WCAG 2.1 AA compliant
- **Performance**: Optimized for Core Web Vitals

## Dependencies

### Required
- **WordPress**: 5.0 or higher
- **PHP**: 7.4 or higher

### Recommended Plugins
- **Polylang**: For advanced multi-language functionality
- **Contact Form 7**: For enhanced contact forms
- **Yoast SEO**: For search engine optimization
- **WP Rocket**: For performance optimization

## Development

### CSS Architecture
- **Mobile-first** responsive design approach
- **CSS Grid and Flexbox** for modern layouts
- **Custom properties** for easy color customization
- **Print styles** included for better printing experience

### JavaScript Features
- **Vanilla JavaScript** with jQuery for compatibility
- **AJAX form submissions** with proper error handling
- **Smooth scrolling** and navigation enhancements
- **Product comparison** functionality
- **Mobile menu** with touch-friendly interactions

### Performance Optimizations
- **Lazy loading** for images
- **Debounced scroll events** for better performance
- **Minification ready** CSS and JS structure
- **Service Worker** support for offline functionality

## Customization Guide

### Colors
Primary colors can be customized in the CSS custom properties:
```css
:root {
    --primary-color: #3498db;
    --secondary-color: #27ae60;
    --text-color: #2c3e50;
    --background-color: #ffffff;
}
```

### Typography
Font families can be changed in the `volkena_scripts()` function in `functions.php`.

### Layout
Grid layouts use CSS Grid with fallbacks for older browsers. Modify grid templates in the respective CSS sections.

## Support

For theme support and customization requests, please contact the development team.

## License

This theme is proprietary software developed specifically for Völkena. All rights reserved.

## Changelog

### Version 1.0.0
- Initial release
- Complete theme functionality
- Multi-language support
- Product and service management
- Contact and communication features
- Responsive design implementation
- Accessibility compliance
- Performance optimizations

---

**Developed with German precision for Völkena - Premium Hearing Solutions Since 2000**
