<?php
/**
 * The template for displaying product archive
 *
 * @package Volkena
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <!-- Page Header -->
    <section class="page-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 100px 0 60px;">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title"><?php _e('Our Products', 'volkena'); ?></h1>
                <p class="page-description"><?php _e('Discover our complete range of premium hearing aids, engineered with German precision and advanced technology.', 'volkena'); ?></p>
            </div>
        </div>
    </section>

    <!-- Product Filters -->
    <section class="product-filters" style="background: white; padding: 2rem 0; border-bottom: 1px solid #ecf0f1;">
        <div class="container">
            <div class="filters-row">
                <div class="filter-group">
                    <label><?php _e('Filter by Type:', 'volkena'); ?></label>
                    <div class="filter-buttons">
                        <button class="product-filter-btn active" data-filter="all"><?php _e('All Products', 'volkena'); ?></button>
                        <button class="product-filter-btn" data-filter="CIC"><?php _e('CIC', 'volkena'); ?></button>
                        <button class="product-filter-btn" data-filter="BTE"><?php _e('BTE', 'volkena'); ?></button>
                        <button class="product-filter-btn" data-filter="ITE"><?php _e('ITE', 'volkena'); ?></button>
                    </div>
                </div>
                
                <div class="sort-group">
                    <label for="product-sort"><?php _e('Sort by:', 'volkena'); ?></label>
                    <select id="product-sort">
                        <option value="default"><?php _e('Default', 'volkena'); ?></option>
                        <option value="name"><?php _e('Name A-Z', 'volkena'); ?></option>
                        <option value="price-low"><?php _e('Price: Low to High', 'volkena'); ?></option>
                        <option value="price-high"><?php _e('Price: High to Low', 'volkena'); ?></option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Grid -->
    <section class="section products-section">
        <div class="container">
            <?php if (have_posts()) : ?>
                <div class="product-grid" id="products-container">
                    <?php while (have_posts()) : the_post(); 
                        $product_type = get_post_meta(get_the_ID(), '_product_type', true);
                        $product_price = get_post_meta(get_the_ID(), '_product_price', true);
                        $product_features = volkena_get_product_features(get_the_ID());
                        $product_specifications = get_post_meta(get_the_ID(), '_product_specifications', true);
                    ?>
                        <div class="product-card" data-type="<?php echo esc_attr($product_type); ?>" data-price="<?php echo esc_attr(preg_replace('/[^0-9.]/', '', $product_price)); ?>">
                            <div class="product-image">
                                <?php if (has_post_thumbnail()) : ?>
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </a>
                                <?php else : ?>
                                    <div class="product-placeholder">
                                        <i class="product-icon">🦻</i>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($product_type) : ?>
                                    <div class="product-badge"><?php echo esc_html($product_type); ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="product-content">
                                <h3 class="product-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h3>
                                
                                <?php if ($product_type) : ?>
                                    <div class="product-type">
                                        <?php 
                                        switch($product_type) {
                                            case 'CIC':
                                                _e('Completely-in-Canal', 'volkena');
                                                break;
                                            case 'BTE':
                                                _e('Behind-the-Ear', 'volkena');
                                                break;
                                            case 'ITE':
                                                _e('In-the-Ear', 'volkena');
                                                break;
                                            default:
                                                echo esc_html($product_type);
                                        }
                                        ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="product-description">
                                    <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                </div>
                                
                                <?php if (!empty($product_features)) : ?>
                                    <ul class="product-features">
                                        <?php foreach (array_slice($product_features, 0, 3) as $feature) : ?>
                                            <li><?php echo esc_html($feature); ?></li>
                                        <?php endforeach; ?>
                                        <?php if (count($product_features) > 3) : ?>
                                            <li class="more-features">+ <?php echo count($product_features) - 3; ?> <?php _e('more features', 'volkena'); ?></li>
                                        <?php endif; ?>
                                    </ul>
                                <?php endif; ?>
                                
                                <div class="product-footer">
                                    <?php if ($product_price) : ?>
                                        <div class="product-price"><?php echo esc_html($product_price); ?></div>
                                    <?php endif; ?>
                                    
                                    <div class="product-actions">
                                        <a href="<?php the_permalink(); ?>" class="btn btn-primary btn-sm">
                                            <?php _e('View Details', 'volkena'); ?>
                                        </a>
                                        <button class="btn btn-outline btn-sm compare-btn" data-product-id="<?php echo get_the_ID(); ?>">
                                            <?php _e('Compare', 'volkena'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-wrapper">
                    <?php
                    the_posts_pagination(array(
                        'prev_text' => __('← Previous', 'volkena'),
                        'next_text' => __('Next →', 'volkena'),
                        'before_page_number' => '<span class="screen-reader-text">' . __('Page', 'volkena') . ' </span>',
                    ));
                    ?>
                </div>
                
            <?php else : ?>
                <div class="no-products">
                    <div class="no-products-content">
                        <h2><?php _e('No Products Found', 'volkena'); ?></h2>
                        <p><?php _e('We couldn\'t find any products matching your criteria. Please try adjusting your filters or check back later.', 'volkena'); ?></p>
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary">
                            <?php _e('Back to Homepage', 'volkena'); ?>
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Product Comparison Bar -->
    <div class="compare-bar">
        <div class="container">
            <div class="compare-content">
                <span class="compare-text">
                    <span class="compare-count">0</span> <?php _e('products selected for comparison', 'volkena'); ?>
                </span>
                <button class="btn btn-secondary btn-sm view-comparison">
                    <?php _e('Compare Products', 'volkena'); ?>
                </button>
                <button class="btn btn-outline btn-sm clear-comparison">
                    <?php _e('Clear All', 'volkena'); ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Product Information Section -->
    <section class="section product-info" style="background: #f8f9fa;">
        <div class="container">
            <div class="row">
                <div class="col">
                    <div class="info-content">
                        <h2><?php _e('Understanding Hearing Aid Types', 'volkena'); ?></h2>
                        <p><?php _e('Choose the right hearing aid type based on your lifestyle, hearing loss degree, and personal preferences.', 'volkena'); ?></p>
                        
                        <div class="info-grid">
                            <div class="info-card">
                                <h3><?php _e('CIC - Completely-in-Canal', 'volkena'); ?></h3>
                                <p><?php _e('Nearly invisible when worn, custom-fitted for maximum comfort and discretion.', 'volkena'); ?></p>
                                <ul>
                                    <li><?php _e('Best for mild to moderate hearing loss', 'volkena'); ?></li>
                                    <li><?php _e('Maximum discretion', 'volkena'); ?></li>
                                    <li><?php _e('Custom-fitted', 'volkena'); ?></li>
                                </ul>
                            </div>
                            
                            <div class="info-card">
                                <h3><?php _e('BTE - Behind-the-Ear', 'volkena'); ?></h3>
                                <p><?php _e('Powerful and versatile, suitable for all degrees of hearing loss with easy handling.', 'volkena'); ?></p>
                                <ul>
                                    <li><?php _e('Suitable for all hearing loss degrees', 'volkena'); ?></li>
                                    <li><?php _e('Easy to handle and maintain', 'volkena'); ?></li>
                                    <li><?php _e('Long battery life', 'volkena'); ?></li>
                                </ul>
                            </div>
                            
                            <div class="info-card">
                                <h3><?php _e('ITE - In-the-Ear', 'volkena'); ?></h3>
                                <p><?php _e('Perfect balance of discretion and functionality, custom-made for your ear.', 'volkena'); ?></p>
                                <ul>
                                    <li><?php _e('Custom-made for perfect fit', 'volkena'); ?></li>
                                    <li><?php _e('Easy volume control', 'volkena'); ?></li>
                                    <li><?php _e('Natural sound quality', 'volkena'); ?></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact CTA -->
    <section class="section cta-section" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; text-align: center;">
        <div class="container">
            <h2 style="color: white;"><?php _e('Need Help Choosing?', 'volkena'); ?></h2>
            <p style="color: rgba(255,255,255,0.9);"><?php _e('Our hearing specialists are here to help you find the perfect hearing solution.', 'volkena'); ?></p>
            <div style="margin-top: 2rem;">
                <a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>" class="btn btn-secondary">
                    <?php _e('Get Expert Advice', 'volkena'); ?>
                </a>
                <?php if (get_theme_mod('whatsapp_number')) : ?>
                    <a href="https://wa.me/<?php echo esc_attr(str_replace(array('+', ' ', '-'), '', get_theme_mod('whatsapp_number'))); ?>" class="btn btn-outline" style="margin-left: 1rem; border-color: white; color: white;">
                        <?php _e('WhatsApp Us', 'volkena'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </section>
</main>

<?php get_footer(); ?>
