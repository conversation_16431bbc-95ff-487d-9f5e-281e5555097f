/**
 * <PERSON><PERSON><PERSON> Blocks Styles for Völkena Theme
 */

/* Block Editor Styles */
.volkena-block-preview {
    padding: 20px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    text-align: center;
    background: #f8f9fa;
    margin: 10px 0;
}

.volkena-block-preview h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.volkena-block-preview p {
    color: #7f8c8d;
    margin: 0;
}

/* Völkena Product Grid Block */
.wp-block-volkena-product-grid {
    margin: 2rem 0;
}

.wp-block-volkena-product-grid .product-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.wp-block-volkena-product-grid .filter-btn {
    background: #ecf0f1;
    color: #2c3e50;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.wp-block-volkena-product-grid .filter-btn:hover,
.wp-block-volkena-product-grid .filter-btn.active {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

.wp-block-volkena-product-grid .product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.wp-block-volkena-product-grid .product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.wp-block-volkena-product-grid .product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.wp-block-volkena-product-grid .product-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.wp-block-volkena-product-grid .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.wp-block-volkena-product-grid .product-card:hover .product-image img {
    transform: scale(1.05);
}

.wp-block-volkena-product-grid .product-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #bdc3c7;
}

.wp-block-volkena-product-grid .placeholder-icon {
    font-size: 4rem;
}

.wp-block-volkena-product-grid .product-content {
    padding: 2rem;
}

.wp-block-volkena-product-grid .product-title {
    margin-bottom: 0.5rem;
}

.wp-block-volkena-product-grid .product-title a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.wp-block-volkena-product-grid .product-title a:hover {
    color: #3498db;
}

.wp-block-volkena-product-grid .product-type {
    background: #ecf0f1;
    color: #2c3e50;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.wp-block-volkena-product-grid .product-excerpt {
    color: #7f8c8d;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.wp-block-volkena-product-grid .product-price {
    color: #27ae60;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

.wp-block-volkena-product-grid .product-actions {
    text-align: center;
}

/* Völkena Service Cards Block */
.wp-block-volkena-service-cards {
    margin: 2rem 0;
}

.service-cards-block {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-cards-block .service-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    padding: 2rem;
}

.service-cards-block .service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.service-cards-block .service-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.service-cards-block .service-excerpt {
    color: #7f8c8d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Völkena Contact Info Block */
.wp-block-volkena-contact-info {
    margin: 2rem 0;
}

.contact-info-block {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-info-block .contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-info-block .contact-item:hover {
    background: #ecf0f1;
    transform: translateX(5px);
}

.contact-info-block .contact-item:last-child {
    margin-bottom: 0;
}

.contact-info-block .contact-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.contact-info-block .contact-details {
    flex: 1;
}

.contact-info-block .contact-details strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.contact-info-block .contact-details a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-info-block .contact-details a:hover {
    color: #2980b9;
}

/* Völkena Hero Section Block */
.wp-block-volkena-hero-section {
    margin: 2rem 0;
}

.volkena-hero-section {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 4rem 2rem;
    border-radius: 15px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.volkena-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
    pointer-events: none;
}

.volkena-hero-section .hero-content {
    position: relative;
    z-index: 1;
}

.volkena-hero-section .hero-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: white;
    line-height: 1.2;
}

.volkena-hero-section .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.volkena-hero-section .hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.volkena-hero-section .stat-item {
    text-align: center;
}

.volkena-hero-section .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.volkena-hero-section .stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Button Styles for Blocks */
.wp-block-volkena-product-grid .btn,
.wp-block-volkena-service-cards .btn,
.wp-block-volkena-contact-info .btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.wp-block-volkena-product-grid .btn-primary,
.wp-block-volkena-service-cards .btn-primary,
.wp-block-volkena-contact-info .btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.wp-block-volkena-product-grid .btn-primary:hover,
.wp-block-volkena-service-cards .btn-primary:hover,
.wp-block-volkena-contact-info .btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wp-block-volkena-product-grid .product-grid,
    .service-cards-block {
        grid-template-columns: 1fr;
    }
    
    .wp-block-volkena-product-grid .product-filters {
        flex-direction: column;
        align-items: center;
    }
    
    .wp-block-volkena-product-grid .filter-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .contact-info-block .contact-item {
        flex-direction: column;
        text-align: center;
    }
    
    .volkena-hero-section {
        padding: 3rem 1.5rem;
    }
    
    .volkena-hero-section .hero-title {
        font-size: 2.5rem;
    }
    
    .volkena-hero-section .hero-stats {
        gap: 2rem;
    }
    
    .volkena-hero-section .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .wp-block-volkena-product-grid .product-content,
    .service-cards-block .service-card,
    .contact-info-block {
        padding: 1.5rem;
    }
    
    .wp-block-volkena-product-grid .product-image {
        height: 200px;
    }
    
    .volkena-hero-section {
        padding: 2rem 1rem;
    }
    
    .volkena-hero-section .hero-title {
        font-size: 2rem;
    }
    
    .volkena-hero-section .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }
}

/* Block Alignment */
.wp-block-volkena-product-grid.alignwide,
.wp-block-volkena-service-cards.alignwide,
.wp-block-volkena-contact-info.alignwide,
.wp-block-volkena-hero-section.alignwide {
    width: 100vw;
    max-width: 100vw;
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
}

.wp-block-volkena-product-grid.alignfull,
.wp-block-volkena-service-cards.alignfull,
.wp-block-volkena-contact-info.alignfull,
.wp-block-volkena-hero-section.alignfull {
    width: 100vw;
    max-width: 100vw;
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
}

/* Editor Specific Styles */
.block-editor-page .wp-block-volkena-product-grid,
.block-editor-page .wp-block-volkena-service-cards,
.block-editor-page .wp-block-volkena-contact-info,
.block-editor-page .wp-block-volkena-hero-section {
    margin: 1rem 0;
}

/* Color Palette Classes */
.has-primary-blue-color {
    color: #3498db;
}

.has-primary-blue-background-color {
    background-color: #3498db;
}

.has-secondary-green-color {
    color: #27ae60;
}

.has-secondary-green-background-color {
    background-color: #27ae60;
}

.has-dark-text-color {
    color: #2c3e50;
}

.has-dark-text-background-color {
    background-color: #2c3e50;
}

.has-light-gray-color {
    color: #f8f9fa;
}

.has-light-gray-background-color {
    background-color: #f8f9fa;
}

.has-white-color {
    color: #ffffff;
}

.has-white-background-color {
    background-color: #ffffff;
}
