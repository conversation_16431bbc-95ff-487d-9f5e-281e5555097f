/**
 * Editor Styles for Völkena Theme
 * These styles are applied in the WordPress block editor
 */

/* Import theme fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Base Editor Styles */
.editor-styles-wrapper {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #2c3e50;
}

/* Typography */
.editor-styles-wrapper h1,
.editor-styles-wrapper h2,
.editor-styles-wrapper h3,
.editor-styles-wrapper h4,
.editor-styles-wrapper h5,
.editor-styles-wrapper h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.editor-styles-wrapper h1 {
    font-size: 2.5rem;
    font-weight: 700;
}

.editor-styles-wrapper h2 {
    font-size: 2rem;
    font-weight: 600;
}

.editor-styles-wrapper h3 {
    font-size: 1.5rem;
}

.editor-styles-wrapper h4 {
    font-size: 1.25rem;
}

.editor-styles-wrapper h5 {
    font-size: 1.125rem;
}

.editor-styles-wrapper h6 {
    font-size: 1rem;
    font-weight: 600;
}

.editor-styles-wrapper p {
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.editor-styles-wrapper a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.editor-styles-wrapper a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Lists */
.editor-styles-wrapper ul,
.editor-styles-wrapper ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.editor-styles-wrapper li {
    margin-bottom: 0.5rem;
}

/* Blockquotes */
.editor-styles-wrapper blockquote {
    border-left: 4px solid #3498db;
    padding-left: 2rem;
    margin: 2rem 0;
    font-style: italic;
    color: #7f8c8d;
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    border-radius: 0 8px 8px 0;
}

.editor-styles-wrapper blockquote p {
    margin-bottom: 0;
}

.editor-styles-wrapper blockquote cite {
    display: block;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #95a5a6;
    font-style: normal;
}

.editor-styles-wrapper blockquote cite::before {
    content: "— ";
}

/* Code */
.editor-styles-wrapper code {
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #e74c3c;
}

.editor-styles-wrapper pre {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 2rem 0;
}

.editor-styles-wrapper pre code {
    background: none;
    padding: 0;
    color: inherit;
}

/* Tables */
.editor-styles-wrapper table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.editor-styles-wrapper th,
.editor-styles-wrapper td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

.editor-styles-wrapper th {
    background: #3498db;
    color: white;
    font-weight: 600;
}

.editor-styles-wrapper tr:hover {
    background: #f8f9fa;
}

/* Images */
.editor-styles-wrapper img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
}

.editor-styles-wrapper figure {
    margin: 2rem 0;
}

.editor-styles-wrapper figcaption {
    text-align: center;
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-top: 0.5rem;
    font-style: italic;
}

/* Buttons */
.editor-styles-wrapper .wp-block-button .wp-block-button__link {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    transition: all 0.3s ease;
    border: none;
}

.editor-styles-wrapper .wp-block-button .wp-block-button__link:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.editor-styles-wrapper .wp-block-button.is-style-outline .wp-block-button__link {
    background: transparent;
    color: #3498db;
    border: 2px solid #3498db;
}

.editor-styles-wrapper .wp-block-button.is-style-outline .wp-block-button__link:hover {
    background: #3498db;
    color: white;
}

/* Separators */
.editor-styles-wrapper hr {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, transparent, #3498db, transparent);
    margin: 3rem 0;
}

/* Columns */
.editor-styles-wrapper .wp-block-columns {
    margin-bottom: 2rem;
}

.editor-styles-wrapper .wp-block-column {
    padding: 0 1rem;
}

/* Cover Block */
.editor-styles-wrapper .wp-block-cover {
    border-radius: 15px;
    overflow: hidden;
}

.editor-styles-wrapper .wp-block-cover .wp-block-cover__inner-container {
    padding: 3rem;
}

/* Group Block */
.editor-styles-wrapper .wp-block-group {
    padding: 2rem;
    border-radius: 15px;
    background: #f8f9fa;
    margin: 2rem 0;
}

.editor-styles-wrapper .wp-block-group.has-background {
    padding: 2rem;
}

/* Media & Text */
.editor-styles-wrapper .wp-block-media-text {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Gallery */
.editor-styles-wrapper .wp-block-gallery {
    margin: 2rem 0;
}

.editor-styles-wrapper .wp-block-gallery .blocks-gallery-item {
    border-radius: 8px;
    overflow: hidden;
}

/* Pullquote */
.editor-styles-wrapper .wp-block-pullquote {
    border: none;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 3rem;
    border-radius: 15px;
    text-align: center;
}

.editor-styles-wrapper .wp-block-pullquote blockquote {
    border: none;
    background: none;
    padding: 0;
    color: white;
    font-size: 1.5rem;
    font-weight: 300;
}

.editor-styles-wrapper .wp-block-pullquote cite {
    color: rgba(255,255,255,0.8);
    font-size: 1rem;
}

/* Verse */
.editor-styles-wrapper .wp-block-verse {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
    font-family: 'Georgia', serif;
    font-style: italic;
}

/* Preformatted */
.editor-styles-wrapper .wp-block-preformatted {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 1.5rem;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Custom Color Classes */
.editor-styles-wrapper .has-primary-blue-color {
    color: #3498db;
}

.editor-styles-wrapper .has-primary-blue-background-color {
    background-color: #3498db;
}

.editor-styles-wrapper .has-secondary-green-color {
    color: #27ae60;
}

.editor-styles-wrapper .has-secondary-green-background-color {
    background-color: #27ae60;
}

.editor-styles-wrapper .has-dark-text-color {
    color: #2c3e50;
}

.editor-styles-wrapper .has-dark-text-background-color {
    background-color: #2c3e50;
}

.editor-styles-wrapper .has-light-gray-color {
    color: #f8f9fa;
}

.editor-styles-wrapper .has-light-gray-background-color {
    background-color: #f8f9fa;
}

/* Font Size Classes */
.editor-styles-wrapper .has-small-font-size {
    font-size: 14px;
}

.editor-styles-wrapper .has-regular-font-size {
    font-size: 16px;
}

.editor-styles-wrapper .has-large-font-size {
    font-size: 20px;
}

.editor-styles-wrapper .has-extra-large-font-size {
    font-size: 28px;
}

/* Alignment */
.editor-styles-wrapper .alignleft {
    float: left;
    margin: 0 2rem 1rem 0;
}

.editor-styles-wrapper .alignright {
    float: right;
    margin: 0 0 1rem 2rem;
}

.editor-styles-wrapper .aligncenter {
    display: block;
    margin: 2rem auto;
    text-align: center;
}

/* Wide and Full Width */
.editor-styles-wrapper .alignwide {
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.editor-styles-wrapper .alignfull {
    width: 100vw;
    max-width: 100vw;
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
}

/* Responsive Design */
@media (max-width: 768px) {
    .editor-styles-wrapper {
        font-size: 14px;
    }
    
    .editor-styles-wrapper h1 {
        font-size: 2rem;
    }
    
    .editor-styles-wrapper h2 {
        font-size: 1.75rem;
    }
    
    .editor-styles-wrapper h3 {
        font-size: 1.5rem;
    }
    
    .editor-styles-wrapper .wp-block-cover .wp-block-cover__inner-container {
        padding: 2rem;
    }
    
    .editor-styles-wrapper .wp-block-group {
        padding: 1.5rem;
    }
    
    .editor-styles-wrapper .wp-block-pullquote {
        padding: 2rem;
    }
    
    .editor-styles-wrapper .wp-block-pullquote blockquote {
        font-size: 1.25rem;
    }
    
    .editor-styles-wrapper .alignleft,
    .editor-styles-wrapper .alignright {
        float: none;
        margin: 1rem auto;
        display: block;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .editor-styles-wrapper {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .editor-styles-wrapper h1,
    .editor-styles-wrapper h2,
    .editor-styles-wrapper h3,
    .editor-styles-wrapper h4,
    .editor-styles-wrapper h5,
    .editor-styles-wrapper h6 {
        color: #e0e0e0;
    }
    
    .editor-styles-wrapper .wp-block-group {
        background: #2d2d2d;
    }
    
    .editor-styles-wrapper blockquote {
        background: #2d2d2d;
        color: #bbb;
    }
    
    .editor-styles-wrapper code {
        background: #2d2d2d;
        color: #e74c3c;
    }
    
    .editor-styles-wrapper table {
        background: #2d2d2d;
    }
    
    .editor-styles-wrapper th,
    .editor-styles-wrapper td {
        border-bottom-color: #404040;
    }
    
    .editor-styles-wrapper tr:hover {
        background: #404040;
    }
}
