/**
 * Elementor Custom Styles for Völkena Theme
 */

/* Elementor Widget Overrides */
.elementor-widget-container {
    max-width: 100%;
}

/* Völkena Product Grid Widget */
.volkena-elementor-product-grid {
    width: 100%;
}

.volkena-elementor-product-grid .product-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.volkena-elementor-product-grid .filter-btn {
    background: #ecf0f1;
    color: #2c3e50;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.volkena-elementor-product-grid .filter-btn:hover,
.volkena-elementor-product-grid .filter-btn.active {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

.volkena-elementor-product-grid .product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.volkena-elementor-product-grid .product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.volkena-elementor-product-grid .product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.volkena-elementor-product-grid .product-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.volkena-elementor-product-grid .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.volkena-elementor-product-grid .product-card:hover .product-image img {
    transform: scale(1.05);
}

.volkena-elementor-product-grid .product-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #bdc3c7;
}

.volkena-elementor-product-grid .placeholder-icon {
    font-size: 4rem;
}

.volkena-elementor-product-grid .product-content {
    padding: 2rem;
}

.volkena-elementor-product-grid .product-title {
    margin-bottom: 0.5rem;
}

.volkena-elementor-product-grid .product-title a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.volkena-elementor-product-grid .product-title a:hover {
    color: #3498db;
}

.volkena-elementor-product-grid .product-type {
    background: #ecf0f1;
    color: #2c3e50;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.volkena-elementor-product-grid .product-excerpt {
    color: #7f8c8d;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.volkena-elementor-product-grid .product-price {
    color: #27ae60;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

.volkena-elementor-product-grid .product-actions {
    text-align: center;
}

/* Völkena Service Cards Widget */
.volkena-elementor-service-cards {
    width: 100%;
}

.volkena-elementor-service-cards .service-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.volkena-elementor-service-cards .service-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.volkena-elementor-service-cards .service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.volkena-elementor-service-cards .service-image {
    height: 200px;
    overflow: hidden;
}

.volkena-elementor-service-cards .service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.volkena-elementor-service-cards .service-content {
    padding: 2rem;
}

.volkena-elementor-service-cards .service-title {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.volkena-elementor-service-cards .service-excerpt {
    color: #7f8c8d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Völkena Contact Info Widget */
.volkena-elementor-contact-info {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.volkena-elementor-contact-info .contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.volkena-elementor-contact-info .contact-item:hover {
    background: #ecf0f1;
    transform: translateX(5px);
}

.volkena-elementor-contact-info .contact-item:last-child {
    margin-bottom: 0;
}

.volkena-elementor-contact-info .contact-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.volkena-elementor-contact-info .contact-details {
    flex: 1;
}

.volkena-elementor-contact-info .contact-details strong {
    display: block;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.volkena-elementor-contact-info .contact-details a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.volkena-elementor-contact-info .contact-details a:hover {
    color: #2980b9;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
    color: white;
}

.btn-outline {
    background: transparent;
    color: #3498db;
    border: 2px solid #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .volkena-elementor-product-grid .product-grid,
    .volkena-elementor-service-cards .service-cards-grid {
        grid-template-columns: 1fr;
    }
    
    .volkena-elementor-product-grid .product-filters {
        flex-direction: column;
        align-items: center;
    }
    
    .volkena-elementor-product-grid .filter-btn {
        width: 100%;
        max-width: 200px;
    }
    
    .volkena-elementor-contact-info .contact-item {
        flex-direction: column;
        text-align: center;
    }
    
    .volkena-elementor-contact-info .contact-details {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .volkena-elementor-product-grid .product-content,
    .volkena-elementor-service-cards .service-content,
    .volkena-elementor-contact-info {
        padding: 1.5rem;
    }
    
    .volkena-elementor-product-grid .product-image,
    .volkena-elementor-service-cards .service-image {
        height: 200px;
    }
}

/* Elementor Editor Styles */
.elementor-editor-active .volkena-elementor-product-grid,
.elementor-editor-active .volkena-elementor-service-cards,
.elementor-editor-active .volkena-elementor-contact-info {
    min-height: 100px;
    position: relative;
}

.elementor-editor-active .volkena-elementor-product-grid:empty::before,
.elementor-editor-active .volkena-elementor-service-cards:empty::before,
.elementor-editor-active .volkena-elementor-contact-info:empty::before {
    content: "Völkena Widget - Configure in the sidebar";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-style: italic;
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.slide-up {
    transform: translateY(30px);
    opacity: 0;
    animation: slideUp 0.6s ease forwards;
}

@keyframes slideUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Custom Elementor Section Styles */
.elementor-section.volkena-hero-section {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    position: relative;
    overflow: hidden;
}

.elementor-section.volkena-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
    pointer-events: none;
}

.elementor-section.volkena-products-section {
    background: #f8f9fa;
}

.elementor-section.volkena-services-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
