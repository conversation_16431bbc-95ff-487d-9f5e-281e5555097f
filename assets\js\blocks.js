(function(blocks, element, editor) {
    var el = element.createElement;
    var RichText = editor.RichText;
    var InspectorControls = editor.InspectorControls;
    var PanelBody = wp.components.PanelBody;
    var SelectControl = wp.components.SelectControl;
    var RangeControl = wp.components.RangeControl;
    var ToggleControl = wp.components.ToggleControl;

    // Völkena Product Grid Block
    blocks.registerBlockType('volkena/product-grid', {
        title: 'Völkena Product Grid',
        icon: 'products',
        category: 'volkena',
        attributes: {
            count: {
                type: 'number',
                default: 6
            },
            type: {
                type: 'string',
                default: 'all'
            },
            columns: {
                type: 'number',
                default: 3
            },
            showFilters: {
                type: 'boolean',
                default: true
            }
        },
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;

            return [
                el(InspectorControls, {},
                    el(PanelBody, { title: 'Product Grid Settings', initialOpen: true },
                        el(RangeControl, {
                            label: 'Number of Products',
                            value: attributes.count,
                            onChange: function(value) {
                                setAttributes({ count: value });
                            },
                            min: 1,
                            max: 12
                        }),
                        el(SelectControl, {
                            label: 'Product Type',
                            value: attributes.type,
                            options: [
                                { label: 'All Types', value: 'all' },
                                { label: 'CIC (Completely-in-Canal)', value: 'CIC' },
                                { label: 'BTE (Behind-the-Ear)', value: 'BTE' },
                                { label: 'ITE (In-the-Ear)', value: 'ITE' }
                            ],
                            onChange: function(value) {
                                setAttributes({ type: value });
                            }
                        }),
                        el(RangeControl, {
                            label: 'Columns',
                            value: attributes.columns,
                            onChange: function(value) {
                                setAttributes({ columns: value });
                            },
                            min: 1,
                            max: 4
                        }),
                        el(ToggleControl, {
                            label: 'Show Filter Buttons',
                            checked: attributes.showFilters,
                            onChange: function(value) {
                                setAttributes({ showFilters: value });
                            }
                        })
                    )
                ),
                el('div', { 
                    className: 'volkena-block-preview',
                    style: { 
                        padding: '20px', 
                        border: '2px dashed #3498db', 
                        borderRadius: '8px',
                        textAlign: 'center',
                        backgroundColor: '#f8f9fa'
                    }
                },
                    el('div', { style: { fontSize: '48px', marginBottom: '10px' } }, '🦻'),
                    el('h3', { style: { color: '#2c3e50', marginBottom: '10px' } }, 'Völkena Product Grid'),
                    el('p', { style: { color: '#7f8c8d', margin: '0' } }, 
                        'Showing ' + attributes.count + ' ' + 
                        (attributes.type === 'all' ? 'products' : attributes.type + ' products') + 
                        ' in ' + attributes.columns + ' columns'
                    ),
                    attributes.showFilters && el('p', { style: { color: '#7f8c8d', margin: '5px 0 0 0', fontSize: '14px' } }, 
                        'Filter buttons enabled'
                    )
                )
            ];
        },
        save: function() {
            return null; // Server-side rendering
        }
    });

    // Völkena Service Cards Block
    blocks.registerBlockType('volkena/service-cards', {
        title: 'Völkena Service Cards',
        icon: 'info',
        category: 'volkena',
        attributes: {
            count: {
                type: 'number',
                default: 3
            },
            layout: {
                type: 'string',
                default: 'grid'
            }
        },
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;

            return [
                el(InspectorControls, {},
                    el(PanelBody, { title: 'Service Cards Settings', initialOpen: true },
                        el(RangeControl, {
                            label: 'Number of Services',
                            value: attributes.count,
                            onChange: function(value) {
                                setAttributes({ count: value });
                            },
                            min: 1,
                            max: 6
                        }),
                        el(SelectControl, {
                            label: 'Layout',
                            value: attributes.layout,
                            options: [
                                { label: 'Grid Layout', value: 'grid' },
                                { label: 'List Layout', value: 'list' }
                            ],
                            onChange: function(value) {
                                setAttributes({ layout: value });
                            }
                        })
                    )
                ),
                el('div', { 
                    className: 'volkena-block-preview',
                    style: { 
                        padding: '20px', 
                        border: '2px dashed #27ae60', 
                        borderRadius: '8px',
                        textAlign: 'center',
                        backgroundColor: '#f8f9fa'
                    }
                },
                    el('div', { style: { fontSize: '48px', marginBottom: '10px' } }, '🔧'),
                    el('h3', { style: { color: '#2c3e50', marginBottom: '10px' } }, 'Völkena Service Cards'),
                    el('p', { style: { color: '#7f8c8d', margin: '0' } }, 
                        'Displaying ' + attributes.count + ' services in ' + attributes.layout + ' layout'
                    )
                )
            ];
        },
        save: function() {
            return null; // Server-side rendering
        }
    });

    // Völkena Contact Info Block
    blocks.registerBlockType('volkena/contact-info', {
        title: 'Völkena Contact Info',
        icon: 'phone',
        category: 'volkena',
        attributes: {
            showPhone: {
                type: 'boolean',
                default: true
            },
            showEmail: {
                type: 'boolean',
                default: true
            },
            showWhatsApp: {
                type: 'boolean',
                default: true
            },
            layout: {
                type: 'string',
                default: 'vertical'
            }
        },
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;

            return [
                el(InspectorControls, {},
                    el(PanelBody, { title: 'Contact Info Settings', initialOpen: true },
                        el(ToggleControl, {
                            label: 'Show Phone',
                            checked: attributes.showPhone,
                            onChange: function(value) {
                                setAttributes({ showPhone: value });
                            }
                        }),
                        el(ToggleControl, {
                            label: 'Show Email',
                            checked: attributes.showEmail,
                            onChange: function(value) {
                                setAttributes({ showEmail: value });
                            }
                        }),
                        el(ToggleControl, {
                            label: 'Show WhatsApp',
                            checked: attributes.showWhatsApp,
                            onChange: function(value) {
                                setAttributes({ showWhatsApp: value });
                            }
                        }),
                        el(SelectControl, {
                            label: 'Layout',
                            value: attributes.layout,
                            options: [
                                { label: 'Vertical', value: 'vertical' },
                                { label: 'Horizontal', value: 'horizontal' }
                            ],
                            onChange: function(value) {
                                setAttributes({ layout: value });
                            }
                        })
                    )
                ),
                el('div', { 
                    className: 'volkena-block-preview',
                    style: { 
                        padding: '20px', 
                        border: '2px dashed #e74c3c', 
                        borderRadius: '8px',
                        textAlign: 'center',
                        backgroundColor: '#f8f9fa'
                    }
                },
                    el('div', { style: { fontSize: '48px', marginBottom: '10px' } }, '📞'),
                    el('h3', { style: { color: '#2c3e50', marginBottom: '10px' } }, 'Völkena Contact Info'),
                    el('div', { style: { color: '#7f8c8d' } },
                        attributes.showPhone && el('div', {}, '📞 Phone'),
                        attributes.showEmail && el('div', {}, '✉️ Email'),
                        attributes.showWhatsApp && el('div', {}, '💬 WhatsApp')
                    ),
                    el('p', { style: { color: '#7f8c8d', margin: '10px 0 0 0', fontSize: '14px' } }, 
                        'Layout: ' + attributes.layout
                    )
                )
            ];
        },
        save: function() {
            return null; // Server-side rendering
        }
    });

    // Völkena Hero Section Block
    blocks.registerBlockType('volkena/hero-section', {
        title: 'Völkena Hero Section',
        icon: 'cover-image',
        category: 'volkena',
        attributes: {
            title: {
                type: 'string',
                default: 'Premium Hearing Solutions'
            },
            subtitle: {
                type: 'string',
                default: 'German precision engineering meets advanced hearing technology'
            },
            backgroundImage: {
                type: 'string',
                default: ''
            },
            showStats: {
                type: 'boolean',
                default: true
            }
        },
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;

            return [
                el(InspectorControls, {},
                    el(PanelBody, { title: 'Hero Section Settings', initialOpen: true },
                        el(ToggleControl, {
                            label: 'Show Company Stats',
                            checked: attributes.showStats,
                            onChange: function(value) {
                                setAttributes({ showStats: value });
                            }
                        })
                    )
                ),
                el('div', { 
                    className: 'volkena-hero-block',
                    style: { 
                        padding: '40px 20px', 
                        background: 'linear-gradient(135deg, #3498db, #2980b9)',
                        color: 'white',
                        borderRadius: '8px',
                        textAlign: 'center'
                    }
                },
                    el(RichText, {
                        tagName: 'h1',
                        placeholder: 'Enter hero title...',
                        value: attributes.title,
                        onChange: function(value) {
                            setAttributes({ title: value });
                        },
                        style: { 
                            fontSize: '2.5rem', 
                            marginBottom: '1rem',
                            color: 'white'
                        }
                    }),
                    el(RichText, {
                        tagName: 'p',
                        placeholder: 'Enter hero subtitle...',
                        value: attributes.subtitle,
                        onChange: function(value) {
                            setAttributes({ subtitle: value });
                        },
                        style: { 
                            fontSize: '1.2rem', 
                            marginBottom: '2rem',
                            opacity: '0.9'
                        }
                    }),
                    attributes.showStats && el('div', { 
                        style: { 
                            display: 'flex', 
                            justifyContent: 'center', 
                            gap: '2rem',
                            flexWrap: 'wrap'
                        }
                    },
                        el('div', {},
                            el('div', { style: { fontSize: '2rem', fontWeight: 'bold' } }, '2000'),
                            el('div', { style: { fontSize: '0.9rem', opacity: '0.8' } }, 'Founded')
                        ),
                        el('div', {},
                            el('div', { style: { fontSize: '2rem', fontWeight: 'bold' } }, '24+'),
                            el('div', { style: { fontSize: '0.9rem', opacity: '0.8' } }, 'Years Experience')
                        ),
                        el('div', {},
                            el('div', { style: { fontSize: '2rem', fontWeight: 'bold' } }, '3'),
                            el('div', { style: { fontSize: '0.9rem', opacity: '0.8' } }, 'Product Types')
                        )
                    )
                )
            ];
        },
        save: function(props) {
            var attributes = props.attributes;
            
            return el('div', { className: 'volkena-hero-section' },
                el('div', { className: 'hero-content' },
                    el(RichText.Content, {
                        tagName: 'h1',
                        className: 'hero-title',
                        value: attributes.title
                    }),
                    el(RichText.Content, {
                        tagName: 'p',
                        className: 'hero-subtitle',
                        value: attributes.subtitle
                    }),
                    attributes.showStats && el('div', { className: 'hero-stats' },
                        el('div', { className: 'stat-item' },
                            el('div', { className: 'stat-number' }, '2000'),
                            el('div', { className: 'stat-label' }, 'Founded')
                        ),
                        el('div', { className: 'stat-item' },
                            el('div', { className: 'stat-number' }, '24+'),
                            el('div', { className: 'stat-label' }, 'Years Experience')
                        ),
                        el('div', { className: 'stat-item' },
                            el('div', { className: 'stat-number' }, '3'),
                            el('div', { className: 'stat-label' }, 'Product Types')
                        )
                    )
                )
            );
        }
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor || window.wp.editor
);

// Register custom block category
wp.blocks.getCategories().push({
    slug: 'volkena',
    title: 'Völkena Blocks'
});
