/**
 * Customizer Live Preview JavaScript for Völkena Theme
 */

(function($) {
    'use strict';

    // Update CSS custom properties
    function updateCSSProperty(property, value) {
        document.documentElement.style.setProperty(property, value);
    }

    // Hero Title
    wp.customize('hero_title', function(value) {
        value.bind(function(newval) {
            $('.hero-title').text(newval);
        });
    });

    // Hero Subtitle
    wp.customize('hero_subtitle', function(value) {
        value.bind(function(newval) {
            $('.hero-subtitle').text(newval);
        });
    });

    // Primary Color
    wp.customize('primary_color', function(value) {
        value.bind(function(newval) {
            updateCSSProperty('--primary-color', newval);
            
            // Update specific elements
            $('.btn-primary, .wp-block-button .wp-block-button__link').css({
                'background': 'linear-gradient(135deg, ' + newval + ', ' + adjustBrightness(newval, -20) + ')'
            });
            
            $('.btn-outline').css({
                'color': newval,
                'border-color': newval
            });
            
            $('a').css('color', newval);
            
            $('.hero-section').css({
                'background': 'linear-gradient(135deg, ' + newval + ', ' + adjustBrightness(newval, -20) + ')'
            });
            
            $('.product-type, .filter-btn.active').css('background', newval);
        });
    });

    // Secondary Color
    wp.customize('secondary_color', function(value) {
        value.bind(function(newval) {
            updateCSSProperty('--secondary-color', newval);
            
            $('.btn-secondary').css({
                'background': 'linear-gradient(135deg, ' + newval + ', ' + adjustBrightness(newval, -20) + ')'
            });
            
            $('.product-price').css('color', newval);
        });
    });

    // Text Color
    wp.customize('text_color', function(value) {
        value.bind(function(newval) {
            updateCSSProperty('--text-color', newval);
            
            $('body, h1, h2, h3, h4, h5, h6').css('color', newval);
        });
    });

    // Body Font
    wp.customize('body_font', function(value) {
        value.bind(function(newval) {
            // Load Google Font if needed
            if (newval !== 'system') {
                loadGoogleFont(newval);
            }
            
            updateCSSProperty('--body-font', "'" + newval + "', sans-serif");
            $('body').css('font-family', "'" + newval + "', sans-serif");
        });
    });

    // Heading Font
    wp.customize('heading_font', function(value) {
        value.bind(function(newval) {
            // Load Google Font if needed
            if (newval !== 'system') {
                loadGoogleFont(newval);
            }
            
            updateCSSProperty('--heading-font', "'" + newval + "', sans-serif");
            $('h1, h2, h3, h4, h5, h6').css('font-family', "'" + newval + "', sans-serif");
        });
    });

    // Font Size
    wp.customize('font_size', function(value) {
        value.bind(function(newval) {
            updateCSSProperty('--font-size', newval + 'px');
            $('body').css('font-size', newval + 'px');
        });
    });

    // Company Name
    wp.customize('company_name', function(value) {
        value.bind(function(newval) {
            $('.site-title a, .site-branding h1 a').text(newval);
        });
    });

    // Company Tagline
    wp.customize('company_tagline', function(value) {
        value.bind(function(newval) {
            $('.site-description, .company-tagline').text(newval);
        });
    });

    // Founded Year
    wp.customize('founded_year', function(value) {
        value.bind(function(newval) {
            $('.stat-founded .stat-number, .founded-year').text(newval);
        });
    });

    // Years Experience
    wp.customize('years_experience', function(value) {
        value.bind(function(newval) {
            $('.stat-experience .stat-number, .years-experience').text(newval);
        });
    });

    // Product Types Count
    wp.customize('product_types_count', function(value) {
        value.bind(function(newval) {
            $('.stat-products .stat-number, .product-types-count').text(newval);
        });
    });

    // WhatsApp Number
    wp.customize('whatsapp_number', function(value) {
        value.bind(function(newval) {
            var cleanNumber = newval.replace(/[^0-9+]/g, '');
            $('.whatsapp-link').attr('href', 'https://wa.me/' + cleanNumber.replace(/[^0-9]/g, ''));
            $('.whatsapp-number').text(newval);
            
            if (newval) {
                $('.whatsapp-float').show();
            } else {
                $('.whatsapp-float').hide();
            }
        });
    });

    // Site Layout
    wp.customize('site_layout', function(value) {
        value.bind(function(newval) {
            if (newval === 'boxed') {
                $('.site').css({
                    'max-width': '1200px',
                    'margin': '0 auto',
                    'box-shadow': '0 0 20px rgba(0,0,0,0.1)'
                });
            } else {
                $('.site').css({
                    'max-width': 'none',
                    'margin': '0',
                    'box-shadow': 'none'
                });
            }
        });
    });

    // Header Style
    wp.customize('header_style', function(value) {
        value.bind(function(newval) {
            $('.site-header').removeClass('header-default header-centered header-minimal');
            $('.site-header').addClass('header-' + newval);
            
            if (newval === 'centered') {
                $('.site-header .header-content').css({
                    'flex-direction': 'column',
                    'text-align': 'center'
                });
                $('.site-header .main-navigation').css('margin-top', '1rem');
            } else if (newval === 'minimal') {
                $('.site-header').css('padding', '0.5rem 0');
                $('.site-header .site-branding h1').css('font-size', '1.5rem');
            } else {
                $('.site-header .header-content').css({
                    'flex-direction': 'row',
                    'text-align': 'left'
                });
                $('.site-header .main-navigation').css('margin-top', '0');
                $('.site-header').css('padding', '');
                $('.site-header .site-branding h1').css('font-size', '');
            }
        });
    });

    // Show Breadcrumbs
    wp.customize('show_breadcrumbs', function(value) {
        value.bind(function(newval) {
            if (newval) {
                $('.breadcrumb-section').show();
            } else {
                $('.breadcrumb-section').hide();
            }
        });
    });

    // Show Hero Stats
    wp.customize('show_hero_stats', function(value) {
        value.bind(function(newval) {
            if (newval) {
                $('.hero-stats').show();
            } else {
                $('.hero-stats').hide();
            }
        });
    });

    // Show Testimonials
    wp.customize('show_testimonials', function(value) {
        value.bind(function(newval) {
            if (newval) {
                $('.testimonials-section').show();
            } else {
                $('.testimonials-section').hide();
            }
        });
    });

    // Show Blog Preview
    wp.customize('show_blog_preview', function(value) {
        value.bind(function(newval) {
            if (newval) {
                $('.blog-preview-section').show();
            } else {
                $('.blog-preview-section').hide();
            }
        });
    });

    // Footer Text
    wp.customize('footer_text', function(value) {
        value.bind(function(newval) {
            $('.footer-copyright').html(newval);
        });
    });

    // Show Footer Widgets
    wp.customize('show_footer_widgets', function(value) {
        value.bind(function(newval) {
            if (newval) {
                $('.footer-widgets').show();
            } else {
                $('.footer-widgets').hide();
            }
        });
    });

    // Footer Columns
    wp.customize('footer_columns', function(value) {
        value.bind(function(newval) {
            $('.footer-widgets').removeClass('columns-1 columns-2 columns-3 columns-4');
            $('.footer-widgets').addClass('columns-' + newval);
            
            $('.footer-widgets').css('grid-template-columns', 'repeat(' + newval + ', 1fr)');
        });
    });

    // Social Media Links
    var socialNetworks = ['facebook', 'twitter', 'linkedin', 'instagram', 'youtube', 'xing'];
    
    socialNetworks.forEach(function(network) {
        wp.customize('social_' + network, function(value) {
            value.bind(function(newval) {
                var $link = $('.social-' + network);
                if (newval) {
                    $link.attr('href', newval).show();
                } else {
                    $link.hide();
                }
            });
        });
    });

    // Helper Functions
    function adjustBrightness(hex, percent) {
        // Remove # if present
        hex = hex.replace('#', '');
        
        // Convert to RGB
        var r = parseInt(hex.substr(0, 2), 16);
        var g = parseInt(hex.substr(2, 2), 16);
        var b = parseInt(hex.substr(4, 2), 16);
        
        // Adjust brightness
        r = Math.max(0, Math.min(255, r + (r * percent / 100)));
        g = Math.max(0, Math.min(255, g + (g * percent / 100)));
        b = Math.max(0, Math.min(255, b + (b * percent / 100)));
        
        // Convert back to hex
        return '#' + 
            Math.round(r).toString(16).padStart(2, '0') +
            Math.round(g).toString(16).padStart(2, '0') +
            Math.round(b).toString(16).padStart(2, '0');
    }

    function loadGoogleFont(fontName) {
        var fontUrl = 'https://fonts.googleapis.com/css2?family=' + 
                     fontName.replace(' ', '+') + ':wght@300;400;500;600;700&display=swap';
        
        // Check if font is already loaded
        if (!$('link[href="' + fontUrl + '"]').length) {
            $('<link>')
                .attr('rel', 'stylesheet')
                .attr('href', fontUrl)
                .appendTo('head');
        }
    }

    // Initialize on document ready
    $(document).ready(function() {
        // Add customizer classes for better targeting
        $('body').addClass('customizer-preview');
        
        // Ensure all customizer values are applied
        setTimeout(function() {
            // Trigger a resize event to ensure proper layout
            $(window).trigger('resize');
        }, 100);
    });

})(jQuery);
