/**
 * Front-End Editor JavaScript for Völkena Theme
 */

(function($) {
    'use strict';

    var VolkenaEditor = {
        editMode: false,
        hasChanges: false,
        originalContent: {},
        
        init: function() {
            this.bindEvents();
            this.setupEditableElements();
        },
        
        bindEvents: function() {
            var self = this;
            
            // Toggle edit mode
            $(document).on('click', '#toggle-edit-mode', function(e) {
                e.preventDefault();
                self.toggleEditMode();
            });
            
            // Save changes
            $(document).on('click', '#save-changes', function(e) {
                e.preventDefault();
                self.saveAllChanges();
            });
            
            // Cancel changes
            $(document).on('click', '#cancel-changes', function(e) {
                e.preventDefault();
                if (self.hasChanges && !confirm(volkenaEditor.strings.confirm_cancel)) {
                    return;
                }
                self.cancelChanges();
            });
            
            // Handle editable element clicks
            $(document).on('click', '.volkena-editable', function(e) {
                if (!self.editMode) return;
                
                e.preventDefault();
                e.stopPropagation();
                self.makeElementEditable($(this));
            });
            
            // Handle content changes
            $(document).on('input blur', '.volkena-editable[contenteditable="true"]', function() {
                self.markAsChanged($(this));
            });
            
            // Prevent form submissions in edit mode
            $(document).on('submit', 'form', function(e) {
                if (self.editMode) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Prevent link clicks in edit mode
            $(document).on('click', 'a', function(e) {
                if (self.editMode && $(this).closest('.volkena-toolbar').length === 0) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Handle escape key
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27 && self.editMode) { // Escape key
                    self.toggleEditMode();
                }
            });
            
            // Warn before leaving with unsaved changes
            $(window).on('beforeunload', function() {
                if (self.hasChanges) {
                    return 'You have unsaved changes. Are you sure you want to leave?';
                }
            });
        },
        
        setupEditableElements: function() {
            // Add edit overlays to editable elements
            $('.volkena-editable').each(function() {
                var $element = $(this);
                if (!$element.find('.volkena-edit-overlay').length) {
                    $element.prepend('<div class="volkena-edit-overlay">' + volkenaEditor.strings.click_to_edit + '</div>');
                }
            });
        },
        
        toggleEditMode: function() {
            this.editMode = !this.editMode;
            var $body = $('body');
            var $toggleBtn = $('#toggle-edit-mode');
            var $saveBtn = $('#save-changes');
            var $cancelBtn = $('#cancel-changes');
            
            if (this.editMode) {
                $body.addClass('volkena-editing-mode');
                $toggleBtn.find('.btn-text').text(volkenaEditor.strings.edit_mode_on);
                $toggleBtn.addClass('active');
                $saveBtn.show();
                $cancelBtn.show();
                
                // Store original content
                this.storeOriginalContent();
                
                // Show edit instructions
                this.showEditInstructions();
                
            } else {
                $body.removeClass('volkena-editing-mode');
                $toggleBtn.find('.btn-text').text(volkenaEditor.strings.edit_mode_off);
                $toggleBtn.removeClass('active');
                $saveBtn.hide();
                $cancelBtn.hide();
                
                // Remove contenteditable from all elements
                $('.volkena-editable').removeAttr('contenteditable').removeClass('editing');
                
                // Hide edit instructions
                this.hideEditInstructions();
            }
        },
        
        makeElementEditable: function($element) {
            // Remove contenteditable from other elements
            $('.volkena-editable').not($element).removeAttr('contenteditable').removeClass('editing');
            
            // Make this element editable
            $element.attr('contenteditable', 'true').addClass('editing').focus();
            
            // Store original content if not already stored
            var elementId = this.getElementId($element);
            if (!this.originalContent[elementId]) {
                this.originalContent[elementId] = $element.html();
            }
        },
        
        markAsChanged: function($element) {
            this.hasChanges = true;
            $element.addClass('changed');
            
            // Update save button state
            $('#save-changes').addClass('has-changes');
        },
        
        storeOriginalContent: function() {
            var self = this;
            $('.volkena-editable').each(function() {
                var $element = $(this);
                var elementId = self.getElementId($element);
                self.originalContent[elementId] = $element.html();
            });
        },
        
        getElementId: function($element) {
            var postId = $element.data('post-id') || 0;
            var field = $element.data('field') || '';
            var type = $element.data('type') || 'text';
            return postId + '_' + field + '_' + type;
        },
        
        saveAllChanges: function() {
            var self = this;
            var $saveBtn = $('#save-changes');
            var $changedElements = $('.volkena-editable.changed');
            
            if ($changedElements.length === 0) {
                return;
            }
            
            $saveBtn.prop('disabled', true).find('.btn-text').text(volkenaEditor.strings.saving);
            
            var savePromises = [];
            
            $changedElements.each(function() {
                var $element = $(this);
                var promise = self.saveElement($element);
                savePromises.push(promise);
            });
            
            Promise.all(savePromises).then(function() {
                // All saves successful
                $saveBtn.find('.btn-text').text(volkenaEditor.strings.saved);
                setTimeout(function() {
                    $saveBtn.prop('disabled', false).find('.btn-text').text('Save Changes');
                    $saveBtn.removeClass('has-changes');
                }, 2000);
                
                // Reset change tracking
                self.hasChanges = false;
                $('.volkena-editable').removeClass('changed');
                
                // Show success message
                self.showMessage('success', volkenaEditor.strings.saved);
                
            }).catch(function(error) {
                // Some saves failed
                $saveBtn.prop('disabled', false).find('.btn-text').text('Save Changes');
                self.showMessage('error', volkenaEditor.strings.error);
                console.error('Save error:', error);
            });
        },
        
        saveElement: function($element) {
            var self = this;
            var postId = $element.data('post-id') || 0;
            var field = $element.data('field') || '';
            var type = $element.data('type') || 'text';
            var content = $element.html();
            
            // Clean up content (remove edit overlay)
            var $temp = $('<div>').html(content);
            $temp.find('.volkena-edit-overlay').remove();
            content = $temp.html();
            
            return new Promise(function(resolve, reject) {
                $.ajax({
                    url: volkenaEditor.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'volkena_save_content',
                        nonce: volkenaEditor.nonce,
                        post_id: postId,
                        field: field,
                        type: type,
                        content: content
                    },
                    success: function(response) {
                        if (response.success) {
                            resolve(response);
                        } else {
                            reject(response.data || 'Unknown error');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },
        
        cancelChanges: function() {
            var self = this;
            
            // Restore original content
            $('.volkena-editable').each(function() {
                var $element = $(this);
                var elementId = self.getElementId($element);
                if (self.originalContent[elementId]) {
                    $element.html(self.originalContent[elementId]);
                }
            });
            
            // Reset state
            this.hasChanges = false;
            $('.volkena-editable').removeClass('changed editing').removeAttr('contenteditable');
            $('#save-changes').removeClass('has-changes');
            
            // Exit edit mode
            this.toggleEditMode();
            
            this.showMessage('info', 'Changes cancelled');
        },
        
        showEditInstructions: function() {
            if ($('#volkena-edit-instructions').length) return;
            
            var instructions = $('<div id="volkena-edit-instructions" class="volkena-edit-instructions">' +
                '<div class="instructions-content">' +
                '<h4>Edit Mode Active</h4>' +
                '<p>Click on any highlighted area to edit content. Press Escape to exit edit mode.</p>' +
                '<button class="close-instructions">&times;</button>' +
                '</div>' +
                '</div>');
            
            $('body').append(instructions);
            
            setTimeout(function() {
                instructions.addClass('show');
            }, 100);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                instructions.removeClass('show');
                setTimeout(function() {
                    instructions.remove();
                }, 300);
            }, 5000);
            
            // Manual close
            instructions.on('click', '.close-instructions', function() {
                instructions.removeClass('show');
                setTimeout(function() {
                    instructions.remove();
                }, 300);
            });
        },
        
        hideEditInstructions: function() {
            $('#volkena-edit-instructions').removeClass('show');
            setTimeout(function() {
                $('#volkena-edit-instructions').remove();
            }, 300);
        },
        
        showMessage: function(type, message) {
            var messageEl = $('<div class="volkena-message volkena-message-' + type + '">' + message + '</div>');
            $('body').append(messageEl);
            
            setTimeout(function() {
                messageEl.addClass('show');
            }, 100);
            
            setTimeout(function() {
                messageEl.removeClass('show');
                setTimeout(function() {
                    messageEl.remove();
                }, 300);
            }, 3000);
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        VolkenaEditor.init();
    });
    
    // Add CSS for edit instructions and messages
    $('<style>').text(`
        .volkena-edit-instructions {
            position: fixed;
            top: 70px;
            right: 20px;
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
            z-index: 99998;
            max-width: 300px;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .volkena-edit-instructions.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .volkena-edit-instructions h4 {
            margin: 0 0 10px 0;
            color: #3498db;
        }
        
        .volkena-edit-instructions p {
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .close-instructions {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .volkena-message {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            z-index: 99999;
            transform: translateY(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .volkena-message.show {
            transform: translateY(0);
            opacity: 1;
        }
        
        .volkena-message-success {
            background: #27ae60;
        }
        
        .volkena-message-error {
            background: #e74c3c;
        }
        
        .volkena-message-info {
            background: #3498db;
        }
        
        @media (max-width: 768px) {
            .volkena-edit-instructions {
                right: 10px;
                left: 10px;
                max-width: none;
            }
            
            .volkena-message {
                right: 10px;
                left: 10px;
            }
        }
    `).appendTo('head');

})(jQuery);
