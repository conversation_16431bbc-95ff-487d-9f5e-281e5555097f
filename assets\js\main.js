/**
 * <PERSON><PERSON><PERSON>ena Theme JavaScript
 * 
 * @package Volkena
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // DOM Ready
    $(document).ready(function() {
        initMobileMenu();
        initLanguageSwitcher();
        initBackToTop();
        initContactForm();
        initProductFilters();
        initSmoothScrolling();
        initHeaderScroll();
        initWhatsAppFloat();
    });

    /**
     * Mobile Menu Functionality
     */
    function initMobileMenu() {
        const $mobileToggle = $('.mobile-menu-toggle');
        const $mobileOverlay = $('.mobile-menu-overlay');
        const $mobileClose = $('.mobile-menu-close');
        const $body = $('body');

        $mobileToggle.on('click', function() {
            $mobileOverlay.addClass('active');
            $body.addClass('mobile-menu-open');
            $(this).attr('aria-expanded', 'true');
        });

        $mobileClose.on('click', closeMobileMenu);
        $mobileOverlay.on('click', function(e) {
            if (e.target === this) {
                closeMobileMenu();
            }
        });

        function closeMobileMenu() {
            $mobileOverlay.removeClass('active');
            $body.removeClass('mobile-menu-open');
            $mobileToggle.attr('aria-expanded', 'false');
        }

        // Close mobile menu on escape key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && $mobileOverlay.hasClass('active')) {
                closeMobileMenu();
            }
        });
    }

    /**
     * Language Switcher
     */
    function initLanguageSwitcher() {
        $('.lang-btn').on('click', function(e) {
            e.preventDefault();
            const lang = $(this).data('lang');
            
            // Remove active class from all buttons
            $('.lang-btn').removeClass('active');
            // Add active class to clicked button
            $(this).addClass('active');
            
            // Here you would typically handle the language switching
            // For now, we'll just update the URL or trigger a page reload
            console.log('Language switched to:', lang);
            
            // If using a plugin like Polylang, this would be handled automatically
            // Otherwise, you might need to redirect to the appropriate language URL
        });
    }

    /**
     * Back to Top Button
     */
    function initBackToTop() {
        const $backToTop = $('#back-to-top');
        
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 300) {
                $backToTop.addClass('visible');
            } else {
                $backToTop.removeClass('visible');
            }
        });

        $backToTop.on('click', function() {
            $('html, body').animate({
                scrollTop: 0
            }, 600);
        });
    }

    /**
     * Contact Form Handler
     */
    function initContactForm() {
        $('#contact-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submitBtn = $form.find('button[type="submit"]');
            const $message = $('#form-message');
            
            // Disable submit button
            $submitBtn.prop('disabled', true).text(volkena_ajax.strings.loading);
            
            // Clear previous messages
            $message.removeClass('success error').empty();
            
            // Prepare form data
            const formData = {
                action: 'volkena_contact_form',
                nonce: volkena_ajax.nonce,
                name: $form.find('#contact-name').val(),
                email: $form.find('#contact-email').val(),
                phone: $form.find('#contact-phone').val(),
                message: $form.find('#contact-message').val()
            };
            
            // Send AJAX request
            $.ajax({
                url: volkena_ajax.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        $message.addClass('success').text(response.data);
                        $form[0].reset();
                    } else {
                        $message.addClass('error').text(response.data);
                    }
                },
                error: function() {
                    $message.addClass('error').text(volkena_ajax.strings.error);
                },
                complete: function() {
                    $submitBtn.prop('disabled', false).text($submitBtn.data('original-text') || 'Send Message');
                }
            });
        });
        
        // Store original button text
        $('#contact-form button[type="submit"]').each(function() {
            $(this).data('original-text', $(this).text());
        });
    }

    /**
     * Product Filters
     */
    function initProductFilters() {
        const $filterButtons = $('.product-filter-btn');
        const $products = $('.product-card');
        const $sortSelect = $('#product-sort');

        // Filter functionality
        $filterButtons.on('click', function() {
            const filter = $(this).data('filter');
            
            // Update active button
            $filterButtons.removeClass('active');
            $(this).addClass('active');
            
            // Filter products
            if (filter === 'all') {
                $products.show();
            } else {
                $products.hide();
                $products.filter('[data-type="' + filter + '"]').show();
            }
        });

        // Sort functionality
        $sortSelect.on('change', function() {
            const sortBy = $(this).val();
            const $container = $('.product-grid');
            const $items = $container.children('.product-card');
            
            $items.sort(function(a, b) {
                let aVal, bVal;
                
                switch (sortBy) {
                    case 'name':
                        aVal = $(a).find('.product-title').text();
                        bVal = $(b).find('.product-title').text();
                        return aVal.localeCompare(bVal);
                    case 'price-low':
                        aVal = parseFloat($(a).data('price')) || 0;
                        bVal = parseFloat($(b).data('price')) || 0;
                        return aVal - bVal;
                    case 'price-high':
                        aVal = parseFloat($(a).data('price')) || 0;
                        bVal = parseFloat($(b).data('price')) || 0;
                        return bVal - aVal;
                    default:
                        return 0;
                }
            });
            
            $container.append($items);
        });
    }

    /**
     * Smooth Scrolling for Anchor Links
     */
    function initSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && location.hostname === this.hostname) {
                let target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 80 // Account for fixed header
                    }, 600);
                    return false;
                }
            }
        });
    }

    /**
     * Header Scroll Effect
     */
    function initHeaderScroll() {
        const $header = $('.site-header');
        let lastScrollTop = 0;
        
        $(window).on('scroll', function() {
            const scrollTop = $(this).scrollTop();
            
            if (scrollTop > 100) {
                $header.addClass('scrolled');
            } else {
                $header.removeClass('scrolled');
            }
            
            // Hide/show header on scroll
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                $header.addClass('header-hidden');
            } else {
                $header.removeClass('header-hidden');
            }
            
            lastScrollTop = scrollTop;
        });
    }

    /**
     * WhatsApp Float Animation
     */
    function initWhatsAppFloat() {
        const $whatsappFloat = $('.whatsapp-float');
        
        if ($whatsappFloat.length) {
            // Add pulse animation
            setInterval(function() {
                $whatsappFloat.addClass('pulse');
                setTimeout(function() {
                    $whatsappFloat.removeClass('pulse');
                }, 1000);
            }, 5000);
        }
    }

    /**
     * Form Validation
     */
    function validateForm($form) {
        let isValid = true;
        
        $form.find('[required]').each(function() {
            const $field = $(this);
            const value = $field.val().trim();
            
            if (!value) {
                $field.addClass('error');
                isValid = false;
            } else {
                $field.removeClass('error');
                
                // Email validation
                if ($field.attr('type') === 'email') {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        $field.addClass('error');
                        isValid = false;
                    }
                }
            }
        });
        
        return isValid;
    }

    /**
     * Product Comparison
     */
    window.volkenaCompare = {
        products: [],
        
        add: function(productId) {
            if (this.products.indexOf(productId) === -1 && this.products.length < 3) {
                this.products.push(productId);
                this.updateUI();
            }
        },
        
        remove: function(productId) {
            const index = this.products.indexOf(productId);
            if (index > -1) {
                this.products.splice(index, 1);
                this.updateUI();
            }
        },
        
        updateUI: function() {
            $('.compare-btn').each(function() {
                const productId = $(this).data('product-id');
                if (volkenaCompare.products.indexOf(productId) > -1) {
                    $(this).addClass('active').text('Remove from Compare');
                } else {
                    $(this).removeClass('active').text('Add to Compare');
                }
            });
            
            $('.compare-count').text(this.products.length);
            
            if (this.products.length > 1) {
                $('.compare-bar').addClass('visible');
            } else {
                $('.compare-bar').removeClass('visible');
            }
        },
        
        view: function() {
            if (this.products.length > 1) {
                window.location.href = '/compare/?products=' + this.products.join(',');
            }
        }
    };

    // Initialize comparison functionality
    $(document).on('click', '.compare-btn', function(e) {
        e.preventDefault();
        const productId = $(this).data('product-id');
        
        if ($(this).hasClass('active')) {
            volkenaCompare.remove(productId);
        } else {
            volkenaCompare.add(productId);
        }
    });

    $(document).on('click', '.view-comparison', function(e) {
        e.preventDefault();
        volkenaCompare.view();
    });

    /**
     * Search Enhancement
     */
    $('.search-form input[type="search"]').on('input', function() {
        const query = $(this).val();
        if (query.length > 2) {
            // Here you could implement live search suggestions
            console.log('Searching for:', query);
        }
    });

    /**
     * Accessibility Enhancements
     */
    
    // Skip link functionality
    $('.skip-link').on('click', function(e) {
        const target = $($(this).attr('href'));
        if (target.length) {
            target.focus();
        }
    });
    
    // Keyboard navigation for mobile menu
    $('.mobile-menu-content a').on('keydown', function(e) {
        if (e.keyCode === 9) { // Tab key
            const $links = $('.mobile-menu-content a');
            const currentIndex = $links.index(this);
            
            if (e.shiftKey) {
                // Shift + Tab (previous)
                if (currentIndex === 0) {
                    e.preventDefault();
                    $links.last().focus();
                }
            } else {
                // Tab (next)
                if (currentIndex === $links.length - 1) {
                    e.preventDefault();
                    $links.first().focus();
                }
            }
        }
    });

})(jQuery);

// Additional CSS for JavaScript-enhanced elements
const additionalCSS = `
.site-header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.site-header.header-hidden {
    transform: translateY(-100%);
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background: white;
    padding: 2rem;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.mobile-menu-overlay.active .mobile-menu-content {
    transform: translateX(0);
}

.mobile-menu-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
}

.mobile-nav-menu {
    list-style: none;
    padding: 0;
    margin-top: 3rem;
}

.mobile-nav-menu li {
    margin-bottom: 1rem;
}

.mobile-nav-menu a {
    display: block;
    padding: 1rem 0;
    color: #2c3e50;
    font-weight: 500;
    border-bottom: 1px solid #ecf0f1;
}

.mobile-language-switcher {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #ecf0f1;
}

.whatsapp-float.pulse .whatsapp-button {
    animation: pulse 1s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.form-field.error {
    border-color: #e74c3c !important;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3) !important;
}

#form-message {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 5px;
    display: none;
}

#form-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

#form-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.compare-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #3498db;
    color: white;
    padding: 1rem;
    text-align: center;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.compare-bar.visible {
    transform: translateY(0);
}

.compare-btn.active {
    background: #27ae60;
    color: white;
}

body.mobile-menu-open {
    overflow: hidden;
}

@media (max-width: 768px) {
    .mobile-menu-content {
        width: 100%;
    }
}
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);

// Initialize modal functionality
$(document).on('click', '.contact-btn', function(e) {
    e.preventDefault();
    $('#contact-modal').fadeIn(300);
});

$(document).on('click', '.modal-close', function() {
    $('.modal').fadeOut(300);
});

$(document).on('click', '.modal', function(e) {
    if (e.target === this) {
        $(this).fadeOut(300);
    }
});

// Product inquiry form handler
$('#product-inquiry-form').on('submit', function(e) {
    e.preventDefault();

    const $form = $(this);
    const $submitBtn = $form.find('button[type="submit"]');
    const $message = $('#inquiry-message');

    // Disable submit button
    $submitBtn.prop('disabled', true).text(volkena_ajax.strings.loading);

    // Clear previous messages
    $message.removeClass('success error').empty();

    // Prepare form data
    const formData = {
        action: 'volkena_contact_form',
        nonce: volkena_ajax.nonce,
        name: $form.find('#inquiry-name').val(),
        email: $form.find('#inquiry-email').val(),
        phone: $form.find('#inquiry-phone').val(),
        message: $form.find('#inquiry-message').val(),
        product: $form.find('input[name="product"]').val()
    };

    // Send AJAX request
    $.ajax({
        url: volkena_ajax.ajax_url,
        type: 'POST',
        data: formData,
        success: function(response) {
            if (response.success) {
                $message.addClass('success').text(response.data);
                $form[0].reset();
                setTimeout(function() {
                    $('.modal').fadeOut(300);
                }, 2000);
            } else {
                $message.addClass('error').text(response.data);
            }
        },
        error: function() {
            $message.addClass('error').text(volkena_ajax.strings.error);
        },
        complete: function() {
            $submitBtn.prop('disabled', false).text('Send Inquiry');
        }
    });
});

// Clear comparison functionality
$(document).on('click', '.clear-comparison', function(e) {
    e.preventDefault();
    volkenaCompare.products = [];
    volkenaCompare.updateUI();
});

// Newsletter signup
$('.newsletter-form').on('submit', function(e) {
    e.preventDefault();
    const email = $(this).find('input[type="email"]').val();

    // Here you would typically send the email to your newsletter service
    alert('Thank you for subscribing! (This is a demo - integrate with your newsletter service)');
    $(this)[0].reset();
});

// Lazy loading for images
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// Performance optimization: Debounce scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply debouncing to scroll events
const debouncedScroll = debounce(function() {
    // Scroll-based functionality here
    const scrollTop = $(window).scrollTop();

    // Update header state
    if (scrollTop > 100) {
        $('.site-header').addClass('scrolled');
    } else {
        $('.site-header').removeClass('scrolled');
    }

    // Update back to top button
    if (scrollTop > 300) {
        $('#back-to-top').addClass('visible');
    } else {
        $('#back-to-top').removeClass('visible');
    }
}, 10);

$(window).on('scroll', debouncedScroll);

// Cookie consent (GDPR compliance)
function initCookieConsent() {
    if (!localStorage.getItem('cookieConsent')) {
        const consentBanner = $(`
            <div id="cookie-consent" style="position: fixed; bottom: 0; left: 0; right: 0; background: #2c3e50; color: white; padding: 1rem; z-index: 10000; text-align: center;">
                <p style="margin: 0 0 1rem 0;">This website uses cookies to ensure you get the best experience. <a href="#" style="color: #3498db;">Learn more</a></p>
                <button id="accept-cookies" class="btn btn-primary btn-sm" style="margin-right: 1rem;">Accept</button>
                <button id="decline-cookies" class="btn btn-outline btn-sm" style="border-color: white; color: white;">Decline</button>
            </div>
        `);

        $('body').append(consentBanner);

        $('#accept-cookies').on('click', function() {
            localStorage.setItem('cookieConsent', 'accepted');
            $('#cookie-consent').fadeOut();
        });

        $('#decline-cookies').on('click', function() {
            localStorage.setItem('cookieConsent', 'declined');
            $('#cookie-consent').fadeOut();
        });
    }
}

// Initialize cookie consent
initCookieConsent();

// Service Worker for offline functionality (Progressive Web App)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}

// Add to home screen prompt
let deferredPrompt;
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;

    // Show install button
    const installBtn = $('<button class="btn btn-outline btn-sm install-app" style="position: fixed; top: 100px; right: 20px; z-index: 1000;">📱 Install App</button>');
    $('body').append(installBtn);

    installBtn.on('click', function() {
        installBtn.hide();
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('User accepted the install prompt');
            }
            deferredPrompt = null;
        });
    });
});

// Error handling for images
$('img').on('error', function() {
    $(this).attr('src', 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2JkYzNjNyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4=');
});

// Analytics tracking (placeholder - integrate with your analytics service)
function trackEvent(category, action, label) {
    if (typeof gtag !== 'undefined') {
        gtag('event', action, {
            event_category: category,
            event_label: label
        });
    }
    console.log('Event tracked:', category, action, label);
}

// Track important interactions
$(document).on('click', '.btn-primary', function() {
    trackEvent('Button', 'Click', 'Primary CTA');
});

$(document).on('click', '.product-card a', function() {
    trackEvent('Product', 'View', $(this).closest('.product-card').find('.product-title').text());
});

$(document).on('submit', '#contact-form', function() {
    trackEvent('Form', 'Submit', 'Contact Form');
});

// Initialize everything when DOM is ready
$(document).ready(function() {
    // Add loading states to buttons
    $('.btn').on('click', function() {
        const $btn = $(this);
        if (!$btn.hasClass('loading')) {
            $btn.addClass('loading');
            setTimeout(() => $btn.removeClass('loading'), 2000);
        }
    });

    // Initialize tooltips (if you want to add them)
    $('[data-tooltip]').each(function() {
        const $element = $(this);
        const tooltip = $element.data('tooltip');

        $element.on('mouseenter', function() {
            const $tooltip = $(`<div class="tooltip">${tooltip}</div>`);
            $('body').append($tooltip);

            const rect = this.getBoundingClientRect();
            $tooltip.css({
                position: 'absolute',
                top: rect.top - $tooltip.outerHeight() - 5,
                left: rect.left + (rect.width / 2) - ($tooltip.outerWidth() / 2),
                zIndex: 10000,
                background: '#2c3e50',
                color: 'white',
                padding: '0.5rem',
                borderRadius: '5px',
                fontSize: '0.8rem',
                whiteSpace: 'nowrap'
            });
        });

        $element.on('mouseleave', function() {
            $('.tooltip').remove();
        });
    });

    console.log('Völkena theme initialized successfully!');
});
