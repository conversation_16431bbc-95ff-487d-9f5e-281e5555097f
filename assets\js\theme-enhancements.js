/**
 * <PERSON><PERSON><PERSON><PERSON>hearing Theme Enhancements
 * Advanced interactive features and animations
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initScrollAnimations();
        initInteractiveElements();
        initAdvancedFeatures();
        initAccessibilityFeatures();
    });

    // Scroll-triggered animations
    function initScrollAnimations() {
        // Intersection Observer for better performance
        if ('IntersectionObserver' in window) {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('in-view');
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe all elements with animation classes
            document.querySelectorAll('.animate-on-scroll').forEach(function(el) {
                observer.observe(el);
            });
        } else {
            // Fallback for older browsers
            $(window).on('scroll', function() {
                $('.animate-on-scroll').each(function() {
                    var elementTop = $(this).offset().top;
                    var elementBottom = elementTop + $(this).outerHeight();
                    var viewportTop = $(window).scrollTop();
                    var viewportBottom = viewportTop + $(window).height();

                    if (elementBottom > viewportTop && elementTop < viewportBottom) {
                        $(this).addClass('in-view');
                    }
                });
            });
        }
    }

    // Interactive elements
    function initInteractiveElements() {
        // Enhanced hover effects for cards
        $('.interactive-card, .product-card, .service-card').each(function() {
            var $card = $(this);
            
            $card.on('mouseenter', function() {
                $(this).addClass('hover-active');
            }).on('mouseleave', function() {
                $(this).removeClass('hover-active');
            });
        });

        // Smooth scrolling for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            var target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800, 'easeInOutCubic');
            }
        });

        // Enhanced button interactions
        $('.btn').each(function() {
            var $btn = $(this);
            
            $btn.on('mousedown', function() {
                $(this).addClass('active-press');
            }).on('mouseup mouseleave', function() {
                $(this).removeClass('active-press');
            });
        });

        // Parallax effect for hero section
        if ($(window).width() > 768) {
            $(window).on('scroll', function() {
                var scrolled = $(window).scrollTop();
                var parallax = $('.hero-section');
                var speed = 0.5;
                
                parallax.css('transform', 'translateY(' + (scrolled * speed) + 'px)');
            });
        }
    }

    // Advanced features
    function initAdvancedFeatures() {
        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(function(img) {
                imageObserver.observe(img);
            });
        }

        // Progressive image loading
        $('.progressive-image').each(function() {
            var $container = $(this);
            var $img = $container.find('img');
            
            $img.on('load', function() {
                $container.addClass('loaded');
            });
        });

        // Advanced search functionality
        $('#search-input').on('input', debounce(function() {
            var query = $(this).val();
            if (query.length > 2) {
                performAdvancedSearch(query);
            }
        }, 300));

        // Product quick view
        $('.quick-view-btn').on('click', function(e) {
            e.preventDefault();
            var productId = $(this).data('product-id');
            openQuickView(productId);
        });

        // Advanced filtering
        $('.filter-option').on('change', function() {
            updateProductFilters();
        });

        // Price range slider
        if ($('#price-range').length) {
            initPriceRangeSlider();
        }
    }

    // Accessibility features
    function initAccessibilityFeatures() {
        // Keyboard navigation enhancement
        $('a, button, input, select, textarea').on('focus', function() {
            $(this).addClass('keyboard-focused');
        }).on('blur', function() {
            $(this).removeClass('keyboard-focused');
        });

        // Skip to content link
        $('body').prepend('<a href="#main" class="skip-link">Skip to main content</a>');

        // High contrast mode toggle
        if (localStorage.getItem('high-contrast') === 'true') {
            $('body').addClass('high-contrast');
        }

        $('.high-contrast-toggle').on('click', function() {
            $('body').toggleClass('high-contrast');
            var isHighContrast = $('body').hasClass('high-contrast');
            localStorage.setItem('high-contrast', isHighContrast);
        });

        // Font size adjustment
        var currentFontSize = localStorage.getItem('font-size') || '16';
        $('html').css('font-size', currentFontSize + 'px');

        $('.font-size-btn').on('click', function() {
            var action = $(this).data('action');
            var currentSize = parseInt($('html').css('font-size'));
            var newSize = currentSize;

            if (action === 'increase' && currentSize < 24) {
                newSize = currentSize + 2;
            } else if (action === 'decrease' && currentSize > 12) {
                newSize = currentSize - 2;
            } else if (action === 'reset') {
                newSize = 16;
            }

            $('html').css('font-size', newSize + 'px');
            localStorage.setItem('font-size', newSize);
        });

        // Screen reader announcements
        function announceToScreenReader(message) {
            var announcement = $('<div class="sr-only" aria-live="polite"></div>');
            announcement.text(message);
            $('body').append(announcement);
            
            setTimeout(function() {
                announcement.remove();
            }, 1000);
        }

        // Announce dynamic content changes
        $(document).on('contentChanged', function(e, message) {
            announceToScreenReader(message);
        });
    }

    // Helper functions
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function performAdvancedSearch(query) {
        // Advanced search implementation
        $.ajax({
            url: volkena_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'volkena_advanced_search',
                query: query,
                nonce: volkena_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    displaySearchResults(response.data);
                }
            }
        });
    }

    function displaySearchResults(results) {
        var $resultsContainer = $('#search-results');
        $resultsContainer.empty();

        if (results.length > 0) {
            results.forEach(function(result) {
                var resultHtml = '<div class="search-result-item">' +
                    '<h4><a href="' + result.url + '">' + result.title + '</a></h4>' +
                    '<p>' + result.excerpt + '</p>' +
                    '</div>';
                $resultsContainer.append(resultHtml);
            });
        } else {
            $resultsContainer.html('<p>No results found.</p>');
        }

        $resultsContainer.show();
    }

    function openQuickView(productId) {
        // Quick view implementation
        var modal = $('<div class="quick-view-modal">' +
            '<div class="modal-content">' +
            '<div class="modal-header">' +
            '<h3>Quick View</h3>' +
            '<button class="modal-close">&times;</button>' +
            '</div>' +
            '<div class="modal-body">' +
            '<div class="loading">Loading...</div>' +
            '</div>' +
            '</div>' +
            '</div>');

        $('body').append(modal);
        modal.fadeIn();

        // Load product data
        $.ajax({
            url: volkena_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'volkena_quick_view',
                product_id: productId,
                nonce: volkena_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    modal.find('.modal-body').html(response.data.html);
                }
            }
        });

        // Close modal
        modal.on('click', '.modal-close, .quick-view-modal', function(e) {
            if (e.target === this) {
                modal.fadeOut(function() {
                    modal.remove();
                });
            }
        });
    }

    function updateProductFilters() {
        var filters = {};
        
        $('.filter-option:checked').each(function() {
            var filterType = $(this).data('filter-type');
            var filterValue = $(this).val();
            
            if (!filters[filterType]) {
                filters[filterType] = [];
            }
            filters[filterType].push(filterValue);
        });

        // Apply filters via AJAX
        $.ajax({
            url: volkena_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'volkena_filter_products',
                filters: filters,
                nonce: volkena_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#products-container').html(response.data.html);
                    $(document).trigger('contentChanged', 'Products filtered');
                }
            }
        });
    }

    function initPriceRangeSlider() {
        var $slider = $('#price-range');
        var min = parseInt($slider.data('min'));
        var max = parseInt($slider.data('max'));

        $slider.slider({
            range: true,
            min: min,
            max: max,
            values: [min, max],
            slide: function(event, ui) {
                $('#price-range-display').text('€' + ui.values[0] + ' - €' + ui.values[1]);
            },
            stop: function(event, ui) {
                updateProductFilters();
            }
        });
    }

    // Custom easing function
    $.easing.easeInOutCubic = function(x, t, b, c, d) {
        if ((t /= d / 2) < 1) return c / 2 * t * t * t + b;
        return c / 2 * ((t -= 2) * t * t + 2) + b;
    };

})(jQuery);
