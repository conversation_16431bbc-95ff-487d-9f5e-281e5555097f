/**
 * <PERSON>ölkenahearing WooCommerce JavaScript
 * Enhanced e-commerce functionality for hearing aid products
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        initWooCommerce();
        initProductComparison();
        initWishlist();
        initQuickView();
        initCartAnimations();
    });

    // Main WooCommerce initialization
    function initWooCommerce() {
        // Update cart count on AJAX add to cart
        $(document.body).on('added_to_cart', function(event, fragments, cart_hash, $button) {
            updateCartCount();
            showCartNotification('Product added to cart successfully!');
        });

        // Enhanced add to cart button
        $('.single_add_to_cart_button').on('click', function(e) {
            var $button = $(this);
            $button.addClass('loading');
            
            setTimeout(function() {
                $button.removeClass('loading');
            }, 2000);
        });

        // Quantity input enhancements
        $('.quantity input[type="number"]').on('change', function() {
            var quantity = $(this).val();
            var $priceElement = $('.price .amount');
            var basePrice = parseFloat($priceElement.data('base-price'));
            
            if (basePrice && quantity) {
                var newPrice = basePrice * quantity;
                $priceElement.text('€' + newPrice.toFixed(2));
            }
        });
    }

    // Product comparison functionality
    function initProductComparison() {
        var compareList = JSON.parse(localStorage.getItem('volkena_compare') || '[]');
        
        // Add to compare button
        $(document).on('click', '.add-to-compare', function(e) {
            e.preventDefault();
            
            var productId = $(this).data('product-id');
            var productTitle = $(this).data('product-title');
            var productImage = $(this).data('product-image');
            var productPrice = $(this).data('product-price');
            
            if (compareList.length >= 3) {
                showNotification('You can only compare up to 3 products at once.', 'warning');
                return;
            }
            
            if (compareList.find(item => item.id === productId)) {
                showNotification('Product is already in comparison list.', 'info');
                return;
            }
            
            compareList.push({
                id: productId,
                title: productTitle,
                image: productImage,
                price: productPrice
            });
            
            localStorage.setItem('volkena_compare', JSON.stringify(compareList));
            updateCompareCount();
            showNotification('Product added to comparison!', 'success');
        });

        // Remove from compare
        $(document).on('click', '.remove-from-compare', function(e) {
            e.preventDefault();
            
            var productId = $(this).data('product-id');
            compareList = compareList.filter(item => item.id !== productId);
            
            localStorage.setItem('volkena_compare', JSON.stringify(compareList));
            updateCompareCount();
            showNotification('Product removed from comparison.', 'info');
        });

        // Update compare count on page load
        updateCompareCount();
    }

    // Wishlist functionality
    function initWishlist() {
        var wishlist = JSON.parse(localStorage.getItem('volkena_wishlist') || '[]');
        
        // Add to wishlist
        $(document).on('click', '.add-to-wishlist', function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var productId = $button.data('product-id');
            
            if (wishlist.includes(productId)) {
                wishlist = wishlist.filter(id => id !== productId);
                $button.removeClass('in-wishlist').addClass('not-in-wishlist');
                showNotification('Removed from wishlist', 'info');
            } else {
                wishlist.push(productId);
                $button.removeClass('not-in-wishlist').addClass('in-wishlist');
                showNotification('Added to wishlist!', 'success');
            }
            
            localStorage.setItem('volkena_wishlist', JSON.stringify(wishlist));
            updateWishlistCount();
        });

        // Update wishlist states on page load
        $('.add-to-wishlist').each(function() {
            var productId = $(this).data('product-id');
            if (wishlist.includes(productId)) {
                $(this).addClass('in-wishlist');
            } else {
                $(this).addClass('not-in-wishlist');
            }
        });

        updateWishlistCount();
    }

    // Quick view functionality
    function initQuickView() {
        $(document).on('click', '.quick-view-button', function(e) {
            e.preventDefault();
            
            var productId = $(this).data('product-id');
            var $modal = $('#quick-view-modal');
            
            if (!$modal.length) {
                $('body').append('<div id="quick-view-modal" class="modal"><div class="modal-content"><div class="modal-header"><h3>Quick View</h3><button class="modal-close">&times;</button></div><div class="modal-body"><div class="loading">Loading...</div></div></div></div>');
                $modal = $('#quick-view-modal');
            }
            
            $modal.show();
            
            // Load product data via AJAX
            $.ajax({
                url: volkena_wc_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'volkena_quick_view',
                    product_id: productId,
                    nonce: volkena_wc_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $modal.find('.modal-body').html(response.data.html);
                    } else {
                        $modal.find('.modal-body').html('<p>Error loading product details.</p>');
                    }
                },
                error: function() {
                    $modal.find('.modal-body').html('<p>Error loading product details.</p>');
                }
            });
        });

        // Close modal
        $(document).on('click', '.modal-close, .modal', function(e) {
            if (e.target === this) {
                $('.modal').hide();
            }
        });
    }

    // Cart animations and enhancements
    function initCartAnimations() {
        // Animate cart icon when item is added
        $(document.body).on('added_to_cart', function() {
            var $cartIcon = $('.header-cart .cart-icon');
            $cartIcon.addClass('bounce');
            
            setTimeout(function() {
                $cartIcon.removeClass('bounce');
            }, 600);
        });

        // Mini cart hover effects
        $('.header-cart').hover(
            function() {
                $(this).addClass('hover');
            },
            function() {
                $(this).removeClass('hover');
            }
        );
    }

    // Helper functions
    function updateCartCount() {
        $.ajax({
            url: volkena_wc_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'volkena_get_cart_count',
                nonce: volkena_wc_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('.cart-count').text(response.data.count);
                }
            }
        });
    }

    function updateCompareCount() {
        var compareList = JSON.parse(localStorage.getItem('volkena_compare') || '[]');
        $('.compare-count').text(compareList.length);
        
        if (compareList.length > 0) {
            $('.compare-widget').addClass('has-items');
        } else {
            $('.compare-widget').removeClass('has-items');
        }
    }

    function updateWishlistCount() {
        var wishlist = JSON.parse(localStorage.getItem('volkena_wishlist') || '[]');
        $('.wishlist-count').text(wishlist.length);
        
        if (wishlist.length > 0) {
            $('.wishlist-widget').addClass('has-items');
        } else {
            $('.wishlist-widget').removeClass('has-items');
        }
    }

    function showNotification(message, type = 'info') {
        var $notification = $('<div class="volkena-notification ' + type + '">' + message + '</div>');
        $('body').append($notification);
        
        setTimeout(function() {
            $notification.addClass('show');
        }, 100);
        
        setTimeout(function() {
            $notification.removeClass('show');
            setTimeout(function() {
                $notification.remove();
            }, 300);
        }, 3000);
    }

    function showCartNotification(message) {
        showNotification(message, 'success');
    }

    // Scroll-triggered animations for product elements
    function initScrollAnimations() {
        var $animatedElements = $('.animate-on-scroll');
        
        $(window).on('scroll', function() {
            $animatedElements.each(function() {
                var $element = $(this);
                var elementTop = $element.offset().top;
                var elementBottom = elementTop + $element.outerHeight();
                var viewportTop = $(window).scrollTop();
                var viewportBottom = viewportTop + $(window).height();
                
                if (elementBottom > viewportTop && elementTop < viewportBottom) {
                    $element.addClass('in-view');
                }
            });
        });
        
        // Trigger on page load
        $(window).trigger('scroll');
    }

    // Initialize scroll animations
    initScrollAnimations();

})(jQuery);
