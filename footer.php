<?php
/**
 * The template for displaying the footer
 *
 * @package Volkena
 * @since 1.0.0
 */
?>

    <footer id="colophon" class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><?php _e('Völk<PERSON>', 'volkena'); ?></h3>
                    <p><?php _e('German precision engineering meets advanced hearing technology. Providing premium hearing solutions since 2000.', 'volkena'); ?></p>
                    <div class="footer-social">
                        <?php if (get_theme_mod('whatsapp_number')) : ?>
                            <a href="https://wa.me/<?php echo esc_attr(str_replace(array('+', ' ', '-'), '', get_theme_mod('whatsapp_number'))); ?>" class="social-link whatsapp" target="_blank" rel="noopener">
                                <span class="screen-reader-text"><?php _e('WhatsApp', 'volkena'); ?></span>
                                📱
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="footer-section">
                    <h3><?php _e('Products', 'volkena'); ?></h3>
                    <ul class="footer-menu">
                        <li><a href="<?php echo esc_url(get_post_type_archive_link('product')); ?>"><?php _e('All Products', 'volkena'); ?></a></li>
                        <li><a href="<?php echo esc_url(add_query_arg('product_type', 'CIC', get_post_type_archive_link('product'))); ?>"><?php _e('CIC Hearing Aids', 'volkena'); ?></a></li>
                        <li><a href="<?php echo esc_url(add_query_arg('product_type', 'BTE', get_post_type_archive_link('product'))); ?>"><?php _e('BTE Hearing Aids', 'volkena'); ?></a></li>
                        <li><a href="<?php echo esc_url(add_query_arg('product_type', 'ITE', get_post_type_archive_link('product'))); ?>"><?php _e('ITE Hearing Aids', 'volkena'); ?></a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3><?php _e('Services', 'volkena'); ?></h3>
                    <ul class="footer-menu">
                        <li><a href="<?php echo esc_url(get_post_type_archive_link('service')); ?>"><?php _e('All Services', 'volkena'); ?></a></li>
                        <li><a href="#"><?php _e('Hearing Aid Fitting', 'volkena'); ?></a></li>
                        <li><a href="#"><?php _e('Repair Services', 'volkena'); ?></a></li>
                        <li><a href="#"><?php _e('Accessories', 'volkena'); ?></a></li>
                        <li><a href="#"><?php _e('Maintenance', 'volkena'); ?></a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3><?php _e('Company', 'volkena'); ?></h3>
                    <ul class="footer-menu">
                        <?php
                        $about_page = get_page_by_path('about');
                        if ($about_page) :
                        ?>
                            <li><a href="<?php echo esc_url(get_permalink($about_page)); ?>"><?php _e('About Us', 'volkena'); ?></a></li>
                        <?php endif; ?>
                        
                        <?php
                        $contact_page = get_page_by_path('contact');
                        if ($contact_page) :
                        ?>
                            <li><a href="<?php echo esc_url(get_permalink($contact_page)); ?>"><?php _e('Contact', 'volkena'); ?></a></li>
                        <?php endif; ?>
                        
                        <?php if (get_option('page_for_posts')) : ?>
                            <li><a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>"><?php _e('Blog', 'volkena'); ?></a></li>
                        <?php endif; ?>
                        
                        <li><a href="#"><?php _e('Privacy Policy', 'volkena'); ?></a></li>
                        <li><a href="#"><?php _e('Terms of Service', 'volkena'); ?></a></li>
                    </ul>
                </div>

                <?php if (is_active_sidebar('footer-1') || is_active_sidebar('footer-2') || is_active_sidebar('footer-3') || is_active_sidebar('footer-4')) : ?>
                    <?php if (is_active_sidebar('footer-1')) : ?>
                        <div class="footer-section">
                            <?php dynamic_sidebar('footer-1'); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (is_active_sidebar('footer-2')) : ?>
                        <div class="footer-section">
                            <?php dynamic_sidebar('footer-2'); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (is_active_sidebar('footer-3')) : ?>
                        <div class="footer-section">
                            <?php dynamic_sidebar('footer-3'); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (is_active_sidebar('footer-4')) : ?>
                        <div class="footer-section">
                            <?php dynamic_sidebar('footer-4'); ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="copyright">
                        <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. <?php _e('All rights reserved.', 'volkena'); ?></p>
                        <p><?php _e('German engineering excellence since 2000', 'volkena'); ?></p>
                    </div>
                    
                    <div class="footer-nav">
                        <?php
                        wp_nav_menu(array(
                            'theme_location' => 'footer',
                            'menu_class'     => 'footer-nav-menu',
                            'container'      => false,
                            'depth'          => 1,
                            'fallback_cb'    => false,
                        ));
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </footer><!-- #colophon -->

    <!-- WhatsApp Floating Button -->
    <?php if (get_theme_mod('whatsapp_number')) : ?>
        <div class="whatsapp-float">
            <a href="https://wa.me/<?php echo esc_attr(str_replace(array('+', ' ', '-'), '', get_theme_mod('whatsapp_number'))); ?>" 
               target="_blank" 
               rel="noopener"
               class="whatsapp-button"
               title="<?php _e('Contact us on WhatsApp', 'volkena'); ?>">
                <span class="whatsapp-icon">💬</span>
                <span class="whatsapp-text"><?php _e('WhatsApp', 'volkena'); ?></span>
            </a>
        </div>
    <?php endif; ?>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" title="<?php _e('Back to top', 'volkena'); ?>">
        <span class="screen-reader-text"><?php _e('Back to top', 'volkena'); ?></span>
        ↑
    </button>

</div><!-- #page -->

<?php wp_footer(); ?>

<!-- Additional Styles for Footer Elements -->
<style>
.footer-menu {
    list-style: none;
    padding: 0;
}

.footer-menu li {
    margin-bottom: 0.5rem;
}

.footer-menu a {
    color: #bdc3c7;
    transition: color 0.3s ease;
}

.footer-menu a:hover {
    color: #3498db;
}

.footer-social {
    margin-top: 1rem;
}

.social-link {
    display: inline-block;
    margin-right: 1rem;
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.social-link:hover {
    transform: scale(1.2);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.footer-nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.footer-nav-menu a {
    color: #bdc3c7;
    font-size: 0.9rem;
}

.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.whatsapp-button {
    display: flex;
    align-items: center;
    background: #25d366;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
}

.whatsapp-button:hover {
    background: #128c7e;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
    color: white;
}

.whatsapp-icon {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.back-to-top {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #3498db;
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .footer-nav-menu {
        flex-direction: column;
        gap: 1rem;
    }
    
    .whatsapp-text {
        display: none;
    }
    
    .whatsapp-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        justify-content: center;
        padding: 0;
    }
}

@media (max-width: 480px) {
    .footer-content {
        grid-template-columns: 1fr;
    }
}
</style>

</body>
</html>
