<?php
/**
 * <PERSON><PERSON>lkena Theme Functions
 *
 * @package Volkena
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define theme constants
define('VOLKENA_VERSION', '1.0.0');
define('VOLKENA_THEME_DIR', get_template_directory());
define('VOLKENA_THEME_URI', get_template_directory_uri());

/**
 * Theme Setup
 */
function volkena_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'volkena'),
        'footer' => __('Footer Menu', 'volkena'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'volkena_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function volkena_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('volkena-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Enqueue Google Fonts
    wp_enqueue_style('volkena-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', array(), null);
    
    // Enqueue main JavaScript
    wp_enqueue_script('volkena-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);
    
    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
    
    // Localize script for AJAX
    wp_localize_script('volkena-main', 'volkena_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('volkena_nonce'),
        'strings' => array(
            'loading' => __('Loading...', 'volkena'),
            'error' => __('An error occurred. Please try again.', 'volkena'),
        )
    ));
}
add_action('wp_enqueue_scripts', 'volkena_scripts');

/**
 * Register Widget Areas
 */
function volkena_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'volkena'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here to appear in your sidebar.', 'volkena'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 1', 'volkena'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in the first footer column.', 'volkena'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 2', 'volkena'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here to appear in the second footer column.', 'volkena'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 3', 'volkena'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here to appear in the third footer column.', 'volkena'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 4', 'volkena'),
        'id'            => 'footer-4',
        'description'   => __('Add widgets here to appear in the fourth footer column.', 'volkena'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'volkena_widgets_init');

/**
 * Custom Post Types
 */
function volkena_register_post_types() {
    // Products Post Type
    register_post_type('product', array(
        'labels' => array(
            'name' => __('Products', 'volkena'),
            'singular_name' => __('Product', 'volkena'),
            'add_new' => __('Add New Product', 'volkena'),
            'add_new_item' => __('Add New Product', 'volkena'),
            'edit_item' => __('Edit Product', 'volkena'),
            'new_item' => __('New Product', 'volkena'),
            'view_item' => __('View Product', 'volkena'),
            'search_items' => __('Search Products', 'volkena'),
            'not_found' => __('No products found', 'volkena'),
            'not_found_in_trash' => __('No products found in trash', 'volkena'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-products',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'products'),
        'show_in_rest' => true,
    ));
    
    // Services Post Type
    register_post_type('service', array(
        'labels' => array(
            'name' => __('Services', 'volkena'),
            'singular_name' => __('Service', 'volkena'),
            'add_new' => __('Add New Service', 'volkena'),
            'add_new_item' => __('Add New Service', 'volkena'),
            'edit_item' => __('Edit Service', 'volkena'),
            'new_item' => __('New Service', 'volkena'),
            'view_item' => __('View Service', 'volkena'),
            'search_items' => __('Search Services', 'volkena'),
            'not_found' => __('No services found', 'volkena'),
            'not_found_in_trash' => __('No services found in trash', 'volkena'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-admin-tools',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'services'),
        'show_in_rest' => true,
    ));
}
add_action('init', 'volkena_register_post_types');

/**
 * Custom Taxonomies
 */
function volkena_register_taxonomies() {
    // Product Categories
    register_taxonomy('product_category', 'product', array(
        'labels' => array(
            'name' => __('Product Categories', 'volkena'),
            'singular_name' => __('Product Category', 'volkena'),
            'search_items' => __('Search Product Categories', 'volkena'),
            'all_items' => __('All Product Categories', 'volkena'),
            'edit_item' => __('Edit Product Category', 'volkena'),
            'update_item' => __('Update Product Category', 'volkena'),
            'add_new_item' => __('Add New Product Category', 'volkena'),
            'new_item_name' => __('New Product Category Name', 'volkena'),
        ),
        'hierarchical' => true,
        'public' => true,
        'show_in_rest' => true,
        'rewrite' => array('slug' => 'product-category'),
    ));
}
add_action('init', 'volkena_register_taxonomies');

/**
 * Custom Meta Boxes
 */
function volkena_add_meta_boxes() {
    add_meta_box(
        'product_details',
        __('Product Details', 'volkena'),
        'volkena_product_details_callback',
        'product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'volkena_add_meta_boxes');

/**
 * Product Details Meta Box Callback
 */
function volkena_product_details_callback($post) {
    wp_nonce_field('volkena_product_details', 'volkena_product_details_nonce');
    
    $price = get_post_meta($post->ID, '_product_price', true);
    $features = get_post_meta($post->ID, '_product_features', true);
    $type = get_post_meta($post->ID, '_product_type', true);
    $specifications = get_post_meta($post->ID, '_product_specifications', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="product_price">' . __('Price', 'volkena') . '</label></th>';
    echo '<td><input type="text" id="product_price" name="product_price" value="' . esc_attr($price) . '" /></td></tr>';
    
    echo '<tr><th><label for="product_type">' . __('Product Type', 'volkena') . '</label></th>';
    echo '<td><select id="product_type" name="product_type">';
    echo '<option value="CIC"' . selected($type, 'CIC', false) . '>CIC (Completely-in-Canal)</option>';
    echo '<option value="BTE"' . selected($type, 'BTE', false) . '>BTE (Behind-the-Ear)</option>';
    echo '<option value="ITE"' . selected($type, 'ITE', false) . '>ITE (In-the-Ear)</option>';
    echo '</select></td></tr>';
    
    echo '<tr><th><label for="product_features">' . __('Features (one per line)', 'volkena') . '</label></th>';
    echo '<td><textarea id="product_features" name="product_features" rows="5" cols="50">' . esc_textarea($features) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="product_specifications">' . __('Specifications', 'volkena') . '</label></th>';
    echo '<td><textarea id="product_specifications" name="product_specifications" rows="5" cols="50">' . esc_textarea($specifications) . '</textarea></td></tr>';
    echo '</table>';
}

/**
 * Save Product Meta Data
 */
function volkena_save_product_meta($post_id) {
    if (!isset($_POST['volkena_product_details_nonce']) || !wp_verify_nonce($_POST['volkena_product_details_nonce'], 'volkena_product_details')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    if (isset($_POST['product_price'])) {
        update_post_meta($post_id, '_product_price', sanitize_text_field($_POST['product_price']));
    }
    
    if (isset($_POST['product_type'])) {
        update_post_meta($post_id, '_product_type', sanitize_text_field($_POST['product_type']));
    }
    
    if (isset($_POST['product_features'])) {
        update_post_meta($post_id, '_product_features', sanitize_textarea_field($_POST['product_features']));
    }
    
    if (isset($_POST['product_specifications'])) {
        update_post_meta($post_id, '_product_specifications', sanitize_textarea_field($_POST['product_specifications']));
    }
}
add_action('save_post', 'volkena_save_product_meta');

/**
 * Language Support Functions
 */
function volkena_load_textdomain() {
    load_theme_textdomain('volkena', get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'volkena_load_textdomain');

/**
 * Custom Functions for Theme Features
 */

// Get current language
function volkena_get_current_language() {
    if (function_exists('pll_current_language')) {
        return pll_current_language();
    }
    return 'de'; // Default to German
}

// Get language switcher
function volkena_language_switcher() {
    if (function_exists('pll_the_languages')) {
        pll_the_languages(array(
            'show_flags' => 0,
            'show_names' => 1,
            'display_names_as' => 'slug'
        ));
    } else {
        // Fallback language switcher
        echo '<div class="language-switcher">';
        echo '<button class="lang-btn active" data-lang="de">DE</button>';
        echo '<button class="lang-btn" data-lang="en">EN</button>';
        echo '</div>';
    }
}

// Get products with filtering
function volkena_get_products($args = array()) {
    $defaults = array(
        'post_type' => 'product',
        'posts_per_page' => -1,
        'post_status' => 'publish',
    );
    
    $args = wp_parse_args($args, $defaults);
    return new WP_Query($args);
}

// Get product features as array
function volkena_get_product_features($post_id) {
    $features = get_post_meta($post_id, '_product_features', true);
    if (empty($features)) {
        return array();
    }
    return array_filter(array_map('trim', explode("\n", $features)));
}

/**
 * AJAX Handlers
 */

// Contact form handler
function volkena_handle_contact_form() {
    check_ajax_referer('volkena_nonce', 'nonce');
    
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $message = sanitize_textarea_field($_POST['message']);
    
    // Validate required fields
    if (empty($name) || empty($email) || empty($message)) {
        wp_send_json_error(__('Please fill in all required fields.', 'volkena'));
    }
    
    if (!is_email($email)) {
        wp_send_json_error(__('Please enter a valid email address.', 'volkena'));
    }
    
    // Send email
    $to = get_option('admin_email');
    $subject = sprintf(__('New Contact Form Submission from %s', 'volkena'), get_bloginfo('name'));
    $body = sprintf(
        __("Name: %s\nEmail: %s\nPhone: %s\n\nMessage:\n%s", 'volkena'),
        $name,
        $email,
        $phone,
        $message
    );
    
    $headers = array('Content-Type: text/html; charset=UTF-8');
    
    if (wp_mail($to, $subject, nl2br($body), $headers)) {
        wp_send_json_success(__('Thank you for your message. We will get back to you soon!', 'volkena'));
    } else {
        wp_send_json_error(__('Sorry, there was an error sending your message. Please try again.', 'volkena'));
    }
}
add_action('wp_ajax_volkena_contact_form', 'volkena_handle_contact_form');
add_action('wp_ajax_nopriv_volkena_contact_form', 'volkena_handle_contact_form');

/**
 * Customizer Settings
 */
function volkena_customize_register($wp_customize) {
    // Hero Section
    $wp_customize->add_section('volkena_hero', array(
        'title' => __('Hero Section', 'volkena'),
        'priority' => 30,
    ));
    
    $wp_customize->add_setting('hero_title', array(
        'default' => __('Premium Hearing Solutions', 'volkena'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('hero_title', array(
        'label' => __('Hero Title', 'volkena'),
        'section' => 'volkena_hero',
        'type' => 'text',
    ));
    
    $wp_customize->add_setting('hero_subtitle', array(
        'default' => __('German precision engineering meets advanced hearing technology', 'volkena'),
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('hero_subtitle', array(
        'label' => __('Hero Subtitle', 'volkena'),
        'section' => 'volkena_hero',
        'type' => 'textarea',
    ));
    
    // Contact Information
    $wp_customize->add_section('volkena_contact', array(
        'title' => __('Contact Information', 'volkena'),
        'priority' => 35,
    ));
    
    $wp_customize->add_setting('whatsapp_number', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('whatsapp_number', array(
        'label' => __('WhatsApp Number', 'volkena'),
        'section' => 'volkena_contact',
        'type' => 'text',
        'description' => __('Enter WhatsApp number with country code (e.g., +49123456789)', 'volkena'),
    ));

    // Colors Section
    $wp_customize->add_section('volkena_colors', array(
        'title' => __('Theme Colors', 'volkena'),
        'priority' => 40,
    ));

    $wp_customize->add_setting('primary_color', array(
        'default' => '#3498db',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'primary_color', array(
        'label' => __('Primary Color', 'volkena'),
        'section' => 'volkena_colors',
        'description' => __('Used for buttons, links, and accents', 'volkena'),
    )));

    $wp_customize->add_setting('secondary_color', array(
        'default' => '#27ae60',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'secondary_color', array(
        'label' => __('Secondary Color', 'volkena'),
        'section' => 'volkena_colors',
        'description' => __('Used for secondary buttons and highlights', 'volkena'),
    )));

    $wp_customize->add_setting('text_color', array(
        'default' => '#2c3e50',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'text_color', array(
        'label' => __('Text Color', 'volkena'),
        'section' => 'volkena_colors',
        'description' => __('Main text color', 'volkena'),
    )));

    // Typography Section
    $wp_customize->add_section('volkena_typography', array(
        'title' => __('Typography', 'volkena'),
        'priority' => 50,
    ));

    $wp_customize->add_setting('body_font', array(
        'default' => 'Inter',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('body_font', array(
        'label' => __('Body Font', 'volkena'),
        'section' => 'volkena_typography',
        'type' => 'select',
        'choices' => array(
            'Inter' => 'Inter',
            'Open Sans' => 'Open Sans',
            'Roboto' => 'Roboto',
            'Lato' => 'Lato',
            'Poppins' => 'Poppins',
            'Montserrat' => 'Montserrat',
        ),
    ));

    $wp_customize->add_setting('heading_font', array(
        'default' => 'Inter',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('heading_font', array(
        'label' => __('Heading Font', 'volkena'),
        'section' => 'volkena_typography',
        'type' => 'select',
        'choices' => array(
            'Inter' => 'Inter',
            'Open Sans' => 'Open Sans',
            'Roboto' => 'Roboto',
            'Lato' => 'Lato',
            'Poppins' => 'Poppins',
            'Montserrat' => 'Montserrat',
        ),
    ));

    $wp_customize->add_setting('font_size', array(
        'default' => '16',
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('font_size', array(
        'label' => __('Base Font Size (px)', 'volkena'),
        'section' => 'volkena_typography',
        'type' => 'range',
        'input_attrs' => array(
            'min' => 14,
            'max' => 20,
            'step' => 1,
        ),
    ));

    // Company Information Section
    $wp_customize->add_section('volkena_company', array(
        'title' => __('Company Information', 'volkena'),
        'priority' => 60,
    ));

    $wp_customize->add_setting('company_name', array(
        'default' => 'Völkena',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('company_name', array(
        'label' => __('Company Name', 'volkena'),
        'section' => 'volkena_company',
        'type' => 'text',
    ));

    $wp_customize->add_setting('company_tagline', array(
        'default' => __('German Precision Hearing Solutions', 'volkena'),
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('company_tagline', array(
        'label' => __('Company Tagline', 'volkena'),
        'section' => 'volkena_company',
        'type' => 'text',
    ));

    $wp_customize->add_setting('founded_year', array(
        'default' => '2000',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('founded_year', array(
        'label' => __('Founded Year', 'volkena'),
        'section' => 'volkena_company',
        'type' => 'text',
    ));

    $wp_customize->add_setting('years_experience', array(
        'default' => '24+',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('years_experience', array(
        'label' => __('Years of Experience', 'volkena'),
        'section' => 'volkena_company',
        'type' => 'text',
    ));

    $wp_customize->add_setting('product_types_count', array(
        'default' => '3',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('product_types_count', array(
        'label' => __('Number of Product Types', 'volkena'),
        'section' => 'volkena_company',
        'type' => 'text',
    ));

    // Layout Options Section
    $wp_customize->add_section('volkena_layout', array(
        'title' => __('Layout Options', 'volkena'),
        'priority' => 70,
    ));

    $wp_customize->add_setting('site_layout', array(
        'default' => 'full-width',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('site_layout', array(
        'label' => __('Site Layout', 'volkena'),
        'section' => 'volkena_layout',
        'type' => 'select',
        'choices' => array(
            'full-width' => __('Full Width', 'volkena'),
            'boxed' => __('Boxed', 'volkena'),
        ),
    ));

    $wp_customize->add_setting('header_style', array(
        'default' => 'default',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('header_style', array(
        'label' => __('Header Style', 'volkena'),
        'section' => 'volkena_layout',
        'type' => 'select',
        'choices' => array(
            'default' => __('Default', 'volkena'),
            'centered' => __('Centered', 'volkena'),
            'minimal' => __('Minimal', 'volkena'),
        ),
    ));

    $wp_customize->add_setting('show_breadcrumbs', array(
        'default' => true,
        'sanitize_callback' => 'volkena_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_breadcrumbs', array(
        'label' => __('Show Breadcrumbs', 'volkena'),
        'section' => 'volkena_layout',
        'type' => 'checkbox',
    ));

    // Homepage Content Section
    $wp_customize->add_section('volkena_homepage', array(
        'title' => __('Homepage Content', 'volkena'),
        'priority' => 80,
    ));

    $wp_customize->add_setting('show_hero_stats', array(
        'default' => true,
        'sanitize_callback' => 'volkena_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_hero_stats', array(
        'label' => __('Show Hero Statistics', 'volkena'),
        'section' => 'volkena_homepage',
        'type' => 'checkbox',
    ));

    $wp_customize->add_setting('featured_products_count', array(
        'default' => '6',
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('featured_products_count', array(
        'label' => __('Featured Products Count', 'volkena'),
        'section' => 'volkena_homepage',
        'type' => 'number',
        'input_attrs' => array(
            'min' => 3,
            'max' => 12,
            'step' => 3,
        ),
    ));

    $wp_customize->add_setting('show_testimonials', array(
        'default' => true,
        'sanitize_callback' => 'volkena_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_testimonials', array(
        'label' => __('Show Testimonials Section', 'volkena'),
        'section' => 'volkena_homepage',
        'type' => 'checkbox',
    ));

    $wp_customize->add_setting('show_blog_preview', array(
        'default' => true,
        'sanitize_callback' => 'volkena_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_blog_preview', array(
        'label' => __('Show Blog Preview', 'volkena'),
        'section' => 'volkena_homepage',
        'type' => 'checkbox',
    ));

    // Footer Content Section
    $wp_customize->add_section('volkena_footer', array(
        'title' => __('Footer Content', 'volkena'),
        'priority' => 90,
    ));

    $wp_customize->add_setting('footer_text', array(
        'default' => __('© 2024 Völkena. All rights reserved. German precision hearing solutions since 2000.', 'volkena'),
        'sanitize_callback' => 'sanitize_textarea_field',
    ));

    $wp_customize->add_control('footer_text', array(
        'label' => __('Footer Copyright Text', 'volkena'),
        'section' => 'volkena_footer',
        'type' => 'textarea',
    ));

    $wp_customize->add_setting('show_footer_widgets', array(
        'default' => true,
        'sanitize_callback' => 'volkena_sanitize_checkbox',
    ));

    $wp_customize->add_control('show_footer_widgets', array(
        'label' => __('Show Footer Widgets', 'volkena'),
        'section' => 'volkena_footer',
        'type' => 'checkbox',
    ));

    $wp_customize->add_setting('footer_columns', array(
        'default' => '4',
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('footer_columns', array(
        'label' => __('Footer Widget Columns', 'volkena'),
        'section' => 'volkena_footer',
        'type' => 'select',
        'choices' => array(
            '1' => '1',
            '2' => '2',
            '3' => '3',
            '4' => '4',
        ),
    ));

    // Social Media Section
    $wp_customize->add_section('volkena_social', array(
        'title' => __('Social Media', 'volkena'),
        'priority' => 100,
    ));

    $social_networks = array(
        'facebook' => 'Facebook',
        'twitter' => 'Twitter',
        'linkedin' => 'LinkedIn',
        'instagram' => 'Instagram',
        'youtube' => 'YouTube',
        'xing' => 'Xing',
    );

    foreach ($social_networks as $network => $label) {
        $wp_customize->add_setting('social_' . $network, array(
            'default' => '',
            'sanitize_callback' => 'esc_url_raw',
        ));

        $wp_customize->add_control('social_' . $network, array(
            'label' => $label . ' ' . __('URL', 'volkena'),
            'section' => 'volkena_social',
            'type' => 'url',
        ));
    }
}
add_action('customize_register', 'volkena_customize_register');

// Sanitization functions
function volkena_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

// Output customizer styles
function volkena_customizer_styles() {
    $primary_color = get_theme_mod('primary_color', '#3498db');
    $secondary_color = get_theme_mod('secondary_color', '#27ae60');
    $text_color = get_theme_mod('text_color', '#2c3e50');
    $body_font = get_theme_mod('body_font', 'Inter');
    $heading_font = get_theme_mod('heading_font', 'Inter');
    $font_size = get_theme_mod('font_size', '16');

    ?>
    <style type="text/css">
        :root {
            --primary-color: <?php echo esc_attr($primary_color); ?>;
            --secondary-color: <?php echo esc_attr($secondary_color); ?>;
            --text-color: <?php echo esc_attr($text_color); ?>;
            --body-font: '<?php echo esc_attr($body_font); ?>', sans-serif;
            --heading-font: '<?php echo esc_attr($heading_font); ?>', sans-serif;
            --font-size: <?php echo esc_attr($font_size); ?>px;
        }

        body {
            font-family: var(--body-font);
            font-size: var(--font-size);
            color: var(--text-color);
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: var(--heading-font);
            color: var(--text-color);
        }

        .btn-primary,
        .wp-block-button .wp-block-button__link {
            background: linear-gradient(135deg, var(--primary-color), color-mix(in srgb, var(--primary-color) 80%, black));
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-color), color-mix(in srgb, var(--secondary-color) 80%, black));
        }

        .btn-outline {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
        }

        a {
            color: var(--primary-color);
        }

        a:hover {
            color: color-mix(in srgb, var(--primary-color) 80%, black);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), color-mix(in srgb, var(--primary-color) 80%, black));
        }

        .product-type,
        .filter-btn.active {
            background: var(--primary-color);
        }

        .product-price {
            color: var(--secondary-color);
        }

        .site-header.scrolled {
            background: rgba(255, 255, 255, 0.95);
        }

        <?php if (get_theme_mod('site_layout') === 'boxed') : ?>
        .site {
            max-width: 1200px;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        <?php endif; ?>

        <?php if (get_theme_mod('header_style') === 'centered') : ?>
        .site-header .header-content {
            flex-direction: column;
            text-align: center;
        }

        .site-header .main-navigation {
            margin-top: 1rem;
        }
        <?php endif; ?>

        <?php if (get_theme_mod('header_style') === 'minimal') : ?>
        .site-header {
            padding: 0.5rem 0;
        }

        .site-header .site-branding h1 {
            font-size: 1.5rem;
        }
        <?php endif; ?>
    </style>
    <?php
}
add_action('wp_head', 'volkena_customizer_styles');

// Enqueue Google Fonts based on customizer settings
function volkena_customizer_fonts() {
    $body_font = get_theme_mod('body_font', 'Inter');
    $heading_font = get_theme_mod('heading_font', 'Inter');

    $fonts = array();
    if ($body_font !== 'system') {
        $fonts[] = $body_font . ':300,400,500,600,700';
    }
    if ($heading_font !== 'system' && $heading_font !== $body_font) {
        $fonts[] = $heading_font . ':300,400,500,600,700';
    }

    if (!empty($fonts)) {
        $font_url = 'https://fonts.googleapis.com/css2?family=' . implode('&family=', $fonts) . '&display=swap';
        wp_enqueue_style('volkena-google-fonts', $font_url, array(), null);
    }
}
add_action('wp_enqueue_scripts', 'volkena_customizer_fonts');

// Live preview JavaScript for customizer
function volkena_customizer_live_preview() {
    wp_enqueue_script(
        'volkena-customizer-preview',
        get_template_directory_uri() . '/assets/js/customizer-preview.js',
        array('jquery', 'customize-preview'),
        VOLKENA_VERSION,
        true
    );
}
add_action('customize_preview_init', 'volkena_customizer_live_preview');

/**
 * ========================================
 * FRONT-END EDITING SYSTEM
 * ========================================
 */

// Add page builder support
function volkena_add_page_builder_support() {
    // Elementor support
    add_theme_support('elementor');
    add_theme_support('elementor-pro');

    // Beaver Builder support
    add_theme_support('fl-builder');

    // Gutenberg support
    add_theme_support('wp-block-styles');
    add_theme_support('align-wide');
    add_theme_support('editor-styles');
    add_editor_style('assets/css/editor-style.css');

    // Custom color palette for Gutenberg
    add_theme_support('editor-color-palette', array(
        array(
            'name' => __('Primary Blue', 'volkena'),
            'slug' => 'primary-blue',
            'color' => '#3498db',
        ),
        array(
            'name' => __('Secondary Green', 'volkena'),
            'slug' => 'secondary-green',
            'color' => '#27ae60',
        ),
        array(
            'name' => __('Dark Text', 'volkena'),
            'slug' => 'dark-text',
            'color' => '#2c3e50',
        ),
        array(
            'name' => __('Light Gray', 'volkena'),
            'slug' => 'light-gray',
            'color' => '#f8f9fa',
        ),
        array(
            'name' => __('White', 'volkena'),
            'slug' => 'white',
            'color' => '#ffffff',
        ),
    ));

    // Custom font sizes for Gutenberg
    add_theme_support('editor-font-sizes', array(
        array(
            'name' => __('Small', 'volkena'),
            'size' => 14,
            'slug' => 'small'
        ),
        array(
            'name' => __('Regular', 'volkena'),
            'size' => 16,
            'slug' => 'regular'
        ),
        array(
            'name' => __('Large', 'volkena'),
            'size' => 20,
            'slug' => 'large'
        ),
        array(
            'name' => __('Extra Large', 'volkena'),
            'size' => 28,
            'slug' => 'extra-large'
        ),
    ));
}
add_action('after_setup_theme', 'volkena_add_page_builder_support');

// Elementor compatibility
function volkena_elementor_compatibility() {
    // Remove default Elementor CSS
    if (class_exists('\Elementor\Plugin')) {
        add_action('wp_enqueue_scripts', function() {
            wp_dequeue_style('elementor-frontend');
            wp_dequeue_style('elementor-post');
            wp_dequeue_style('elementor-pro');
        }, 20);

        // Add custom Elementor styles
        add_action('wp_enqueue_scripts', function() {
            wp_enqueue_style('volkena-elementor', get_template_directory_uri() . '/assets/css/elementor.css', array(), VOLKENA_VERSION);
        });
    }
}
add_action('init', 'volkena_elementor_compatibility');

// Register custom Elementor widgets
function volkena_register_elementor_widgets() {
    if (class_exists('\Elementor\Plugin')) {
        require_once get_template_directory() . '/inc/elementor-widgets.php';
    }
}
add_action('elementor/widgets/widgets_registered', 'volkena_register_elementor_widgets');

// Beaver Builder compatibility
function volkena_beaver_builder_compatibility() {
    if (class_exists('FLBuilder')) {
        // Add custom modules
        add_action('init', function() {
            if (class_exists('FLBuilder')) {
                require_once get_template_directory() . '/inc/beaver-builder-modules.php';
            }
        });
    }
}
add_action('init', 'volkena_beaver_builder_compatibility');

// Gutenberg custom blocks
function volkena_register_gutenberg_blocks() {
    // Register custom blocks
    wp_register_script(
        'volkena-blocks',
        get_template_directory_uri() . '/assets/js/blocks.js',
        array('wp-blocks', 'wp-element', 'wp-editor'),
        VOLKENA_VERSION
    );

    wp_register_style(
        'volkena-blocks-style',
        get_template_directory_uri() . '/assets/css/blocks.css',
        array(),
        VOLKENA_VERSION
    );

    register_block_type('volkena/product-grid', array(
        'editor_script' => 'volkena-blocks',
        'style' => 'volkena-blocks-style',
        'render_callback' => 'volkena_render_product_grid_block',
    ));

    register_block_type('volkena/service-cards', array(
        'editor_script' => 'volkena-blocks',
        'style' => 'volkena-blocks-style',
        'render_callback' => 'volkena_render_service_cards_block',
    ));

    register_block_type('volkena/contact-info', array(
        'editor_script' => 'volkena-blocks',
        'style' => 'volkena-blocks-style',
        'render_callback' => 'volkena_render_contact_info_block',
    ));
}
add_action('init', 'volkena_register_gutenberg_blocks');

// Block render callbacks
function volkena_render_product_grid_block($attributes) {
    $count = isset($attributes['count']) ? $attributes['count'] : 6;
    $type = isset($attributes['type']) ? $attributes['type'] : 'all';

    return volkena_product_grid_shortcode(array('count' => $count, 'type' => $type));
}

function volkena_render_service_cards_block($attributes) {
    $count = isset($attributes['count']) ? $attributes['count'] : 3;

    ob_start();
    $services = new WP_Query(array(
        'post_type' => 'service',
        'posts_per_page' => $count
    ));

    if ($services->have_posts()) {
        echo '<div class="service-cards-block">';
        while ($services->have_posts()) {
            $services->the_post();
            echo '<div class="service-card">';
            echo '<h3>' . get_the_title() . '</h3>';
            echo '<div class="service-excerpt">' . get_the_excerpt() . '</div>';
            echo '<a href="' . get_permalink() . '" class="btn btn-primary">' . __('Learn More', 'volkena') . '</a>';
            echo '</div>';
        }
        echo '</div>';
    }
    wp_reset_postdata();

    return ob_get_clean();
}

function volkena_render_contact_info_block($attributes) {
    ob_start();
    echo '<div class="contact-info-block">';
    echo '<div class="contact-item">';
    echo '<strong>' . __('Phone:', 'volkena') . '</strong> ' . get_theme_mod('contact_phone', '+49 123 456 789');
    echo '</div>';
    echo '<div class="contact-item">';
    echo '<strong>' . __('Email:', 'volkena') . '</strong> ' . get_theme_mod('contact_email', '<EMAIL>');
    echo '</div>';
    if (get_theme_mod('whatsapp_number')) {
        echo '<div class="contact-item">';
        echo '<strong>' . __('WhatsApp:', 'volkena') . '</strong> ' . get_theme_mod('whatsapp_number');
        echo '</div>';
    }
    echo '</div>';

    return ob_get_clean();
}

// Enable front-end editing for all post types
function volkena_enable_frontend_editing() {
    // Add edit links to all content
    if (is_user_logged_in() && current_user_can('edit_posts')) {
        add_filter('the_content', 'volkena_add_edit_links');
        add_filter('the_title', 'volkena_add_title_edit_links', 10, 2);
    }
}
add_action('wp', 'volkena_enable_frontend_editing');

function volkena_add_edit_links($content) {
    if (is_singular() && in_the_loop() && is_main_query()) {
        $edit_link = get_edit_post_link();
        if ($edit_link) {
            $edit_button = '<div class="volkena-edit-controls">';
            $edit_button .= '<a href="' . $edit_link . '" class="volkena-edit-btn" target="_blank">';
            $edit_button .= '<span class="dashicons dashicons-edit"></span> ' . __('Edit Content', 'volkena');
            $edit_button .= '</a>';
            $edit_button .= '</div>';
            $content = $edit_button . $content;
        }
    }
    return $content;
}

function volkena_add_title_edit_links($title, $id) {
    if (is_admin() || !is_user_logged_in() || !current_user_can('edit_posts')) {
        return $title;
    }

    if (is_singular() && in_the_loop() && is_main_query() && $id === get_the_ID()) {
        $edit_link = get_edit_post_link($id);
        if ($edit_link) {
            $title .= ' <a href="' . $edit_link . '" class="volkena-title-edit" target="_blank" title="' . __('Edit Title', 'volkena') . '">';
            $title .= '<span class="dashicons dashicons-edit"></span>';
            $title .= '</a>';
        }
    }
    return $title;
}

// Add inline editing styles
function volkena_frontend_editing_styles() {
    if (is_user_logged_in() && current_user_can('edit_posts')) {
        ?>
        <style>
        .volkena-edit-controls {
            position: relative;
            margin-bottom: 1rem;
            text-align: right;
        }

        .volkena-edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: #0073aa;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 3px;
            text-decoration: none;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .volkena-edit-btn:hover {
            background: #005a87;
            color: white;
        }

        .volkena-title-edit {
            color: #0073aa;
            text-decoration: none;
            margin-left: 0.5rem;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .volkena-title-edit:hover {
            opacity: 1;
            color: #005a87;
        }

        .volkena-title-edit .dashicons {
            font-size: 0.8em;
            vertical-align: middle;
        }
        </style>
        <?php
    }
}
add_action('wp_head', 'volkena_frontend_editing_styles');

/**
 * ========================================
 * FRONT-END CONTENT MANAGEMENT SYSTEM
 * ========================================
 */

// Add front-end editing toolbar
function volkena_frontend_editing_toolbar() {
    if (!is_user_logged_in() || !current_user_can('edit_posts')) {
        return;
    }

    ?>
    <div id="volkena-frontend-toolbar" class="volkena-toolbar">
        <div class="toolbar-content">
            <div class="toolbar-logo">
                <span class="dashicons dashicons-admin-customizer"></span>
                <span class="toolbar-title"><?php _e('Völkena Editor', 'volkena'); ?></span>
            </div>

            <div class="toolbar-actions">
                <button id="toggle-edit-mode" class="toolbar-btn" data-mode="view">
                    <span class="dashicons dashicons-edit"></span>
                    <span class="btn-text"><?php _e('Edit Mode', 'volkena'); ?></span>
                </button>

                <button id="save-changes" class="toolbar-btn toolbar-btn-primary" style="display: none;">
                    <span class="dashicons dashicons-yes"></span>
                    <span class="btn-text"><?php _e('Save Changes', 'volkena'); ?></span>
                </button>

                <button id="cancel-changes" class="toolbar-btn" style="display: none;">
                    <span class="dashicons dashicons-no"></span>
                    <span class="btn-text"><?php _e('Cancel', 'volkena'); ?></span>
                </button>

                <a href="<?php echo admin_url('customize.php'); ?>" class="toolbar-btn">
                    <span class="dashicons dashicons-admin-appearance"></span>
                    <span class="btn-text"><?php _e('Customize', 'volkena'); ?></span>
                </a>

                <?php if (current_user_can('manage_options')) : ?>
                <a href="<?php echo admin_url(); ?>" class="toolbar-btn">
                    <span class="dashicons dashicons-dashboard"></span>
                    <span class="btn-text"><?php _e('Dashboard', 'volkena'); ?></span>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <style>
    .volkena-toolbar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: #23282d;
        color: white;
        z-index: 99999;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .toolbar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        height: 50px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .toolbar-logo {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
    }

    .toolbar-logo .dashicons {
        color: #3498db;
        font-size: 20px;
    }

    .toolbar-actions {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .toolbar-btn {
        display: flex;
        align-items: center;
        gap: 6px;
        background: transparent;
        color: white;
        border: 1px solid #3c434a;
        padding: 8px 12px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 13px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .toolbar-btn:hover {
        background: #3c434a;
        color: white;
        text-decoration: none;
    }

    .toolbar-btn-primary {
        background: #3498db;
        border-color: #3498db;
    }

    .toolbar-btn-primary:hover {
        background: #2980b9;
        border-color: #2980b9;
    }

    .toolbar-btn .dashicons {
        font-size: 16px;
        line-height: 1;
    }

    body.volkena-editing-mode {
        padding-top: 50px;
    }

    body.volkena-editing-mode .volkena-editable {
        position: relative;
        outline: 2px dashed transparent;
        transition: outline 0.3s ease;
    }

    body.volkena-editing-mode .volkena-editable:hover {
        outline-color: #3498db;
    }

    body.volkena-editing-mode .volkena-editable.editing {
        outline-color: #27ae60;
        outline-style: solid;
    }

    .volkena-edit-overlay {
        position: absolute;
        top: -30px;
        left: 0;
        background: #3498db;
        color: white;
        padding: 4px 8px;
        font-size: 12px;
        border-radius: 3px;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    body.volkena-editing-mode .volkena-editable:hover .volkena-edit-overlay {
        opacity: 1;
    }

    @media (max-width: 768px) {
        .toolbar-content {
            padding: 0 15px;
        }

        .toolbar-btn .btn-text {
            display: none;
        }

        .toolbar-actions {
            gap: 5px;
        }

        .toolbar-btn {
            padding: 8px;
        }
    }
    </style>
    <?php
}
add_action('wp_footer', 'volkena_frontend_editing_toolbar');

// Make content areas editable
function volkena_make_content_editable($content, $type = 'text', $field = '', $post_id = 0) {
    if (!is_user_logged_in() || !current_user_can('edit_posts')) {
        return $content;
    }

    $post_id = $post_id ?: get_the_ID();
    $edit_class = 'volkena-editable';
    $data_attrs = 'data-type="' . esc_attr($type) . '" data-field="' . esc_attr($field) . '" data-post-id="' . esc_attr($post_id) . '"';

    return '<div class="' . $edit_class . '" ' . $data_attrs . '>' .
           '<div class="volkena-edit-overlay">' . __('Click to edit', 'volkena') . '</div>' .
           $content .
           '</div>';
}

// AJAX handler for saving content
function volkena_save_frontend_content() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'volkena_frontend_edit')) {
        wp_die(__('Security check failed', 'volkena'));
    }

    // Check permissions
    if (!current_user_can('edit_posts')) {
        wp_die(__('You do not have permission to edit content', 'volkena'));
    }

    $post_id = intval($_POST['post_id']);
    $field = sanitize_text_field($_POST['field']);
    $content = wp_kses_post($_POST['content']);
    $type = sanitize_text_field($_POST['type']);

    if (!$post_id || !$field) {
        wp_send_json_error(__('Invalid data provided', 'volkena'));
    }

    // Handle different content types
    switch ($type) {
        case 'title':
            wp_update_post(array(
                'ID' => $post_id,
                'post_title' => sanitize_text_field($content)
            ));
            break;

        case 'content':
            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $content
            ));
            break;

        case 'excerpt':
            wp_update_post(array(
                'ID' => $post_id,
                'post_excerpt' => $content
            ));
            break;

        case 'meta':
            update_post_meta($post_id, $field, $content);
            break;

        case 'theme_mod':
            set_theme_mod($field, $content);
            break;

        default:
            wp_send_json_error(__('Unknown content type', 'volkena'));
    }

    wp_send_json_success(__('Content saved successfully', 'volkena'));
}
add_action('wp_ajax_volkena_save_content', 'volkena_save_frontend_content');

// Enqueue front-end editing scripts
function volkena_frontend_editing_scripts() {
    if (!is_user_logged_in() || !current_user_can('edit_posts')) {
        return;
    }

    wp_enqueue_script(
        'volkena-frontend-editor',
        get_template_directory_uri() . '/assets/js/frontend-editor.js',
        array('jquery'),
        VOLKENA_VERSION,
        true
    );

    wp_localize_script('volkena-frontend-editor', 'volkenaEditor', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('volkena_frontend_edit'),
        'strings' => array(
            'saving' => __('Saving...', 'volkena'),
            'saved' => __('Saved!', 'volkena'),
            'error' => __('Error saving content', 'volkena'),
            'edit_mode_on' => __('Exit Edit Mode', 'volkena'),
            'edit_mode_off' => __('Edit Mode', 'volkena'),
            'confirm_cancel' => __('Are you sure you want to cancel? Unsaved changes will be lost.', 'volkena'),
        )
    ));
}
add_action('wp_enqueue_scripts', 'volkena_frontend_editing_scripts');

/**
 * ========================================
 * ADMIN-FRIENDLY INTERFACE
 * ========================================
 */

// Add custom admin menu for Völkena settings
function volkena_admin_menu() {
    add_menu_page(
        __('Völkena Settings', 'volkena'),
        __('Völkena', 'volkena'),
        'manage_options',
        'volkena-settings',
        'volkena_admin_page',
        'dashicons-admin-customizer',
        30
    );

    add_submenu_page(
        'volkena-settings',
        __('Theme Settings', 'volkena'),
        __('Theme Settings', 'volkena'),
        'manage_options',
        'volkena-settings',
        'volkena_admin_page'
    );

    add_submenu_page(
        'volkena-settings',
        __('Product Management', 'volkena'),
        __('Products', 'volkena'),
        'edit_posts',
        'edit.php?post_type=product'
    );

    add_submenu_page(
        'volkena-settings',
        __('Service Management', 'volkena'),
        __('Services', 'volkena'),
        'edit_posts',
        'edit.php?post_type=service'
    );

    add_submenu_page(
        'volkena-settings',
        __('Import/Export', 'volkena'),
        __('Import/Export', 'volkena'),
        'manage_options',
        'volkena-import-export',
        'volkena_import_export_page'
    );
}
add_action('admin_menu', 'volkena_admin_menu');

// Main admin page
function volkena_admin_page() {
    if (isset($_POST['submit'])) {
        // Handle form submission
        volkena_save_admin_settings();
        echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'volkena') . '</p></div>';
    }

    ?>
    <div class="wrap volkena-admin-wrap">
        <h1><?php _e('Völkena Theme Settings', 'volkena'); ?></h1>

        <div class="volkena-admin-header">
            <div class="volkena-logo">
                <h2><?php _e('Völkena', 'volkena'); ?></h2>
                <p><?php _e('German Precision Hearing Solutions', 'volkena'); ?></p>
            </div>
            <div class="volkena-version">
                <span><?php _e('Version', 'volkena'); ?> <?php echo VOLKENA_VERSION; ?></span>
            </div>
        </div>

        <div class="volkena-admin-content">
            <div class="volkena-admin-tabs">
                <nav class="nav-tab-wrapper">
                    <a href="#general" class="nav-tab nav-tab-active"><?php _e('General', 'volkena'); ?></a>
                    <a href="#company" class="nav-tab"><?php _e('Company Info', 'volkena'); ?></a>
                    <a href="#contact" class="nav-tab"><?php _e('Contact', 'volkena'); ?></a>
                    <a href="#social" class="nav-tab"><?php _e('Social Media', 'volkena'); ?></a>
                    <a href="#advanced" class="nav-tab"><?php _e('Advanced', 'volkena'); ?></a>
                </nav>

                <form method="post" action="">
                    <?php wp_nonce_field('volkena_admin_settings', 'volkena_nonce'); ?>

                    <!-- General Tab -->
                    <div id="general" class="tab-content active">
                        <h3><?php _e('General Settings', 'volkena'); ?></h3>

                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Site Logo', 'volkena'); ?></th>
                                <td>
                                    <input type="hidden" id="site_logo" name="site_logo" value="<?php echo esc_attr(get_theme_mod('custom_logo')); ?>" />
                                    <div class="logo-preview">
                                        <?php if (get_theme_mod('custom_logo')) : ?>
                                            <img src="<?php echo wp_get_attachment_image_url(get_theme_mod('custom_logo'), 'medium'); ?>" alt="Logo" style="max-width: 200px;" />
                                        <?php endif; ?>
                                    </div>
                                    <button type="button" class="button upload-logo"><?php _e('Upload Logo', 'volkena'); ?></button>
                                    <button type="button" class="button remove-logo" style="<?php echo get_theme_mod('custom_logo') ? '' : 'display:none;'; ?>"><?php _e('Remove Logo', 'volkena'); ?></button>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Favicon', 'volkena'); ?></th>
                                <td>
                                    <input type="hidden" id="site_favicon" name="site_favicon" value="<?php echo esc_attr(get_option('volkena_favicon')); ?>" />
                                    <div class="favicon-preview">
                                        <?php if (get_option('volkena_favicon')) : ?>
                                            <img src="<?php echo wp_get_attachment_image_url(get_option('volkena_favicon'), 'thumbnail'); ?>" alt="Favicon" style="width: 32px; height: 32px;" />
                                        <?php endif; ?>
                                    </div>
                                    <button type="button" class="button upload-favicon"><?php _e('Upload Favicon', 'volkena'); ?></button>
                                    <button type="button" class="button remove-favicon" style="<?php echo get_option('volkena_favicon') ? '' : 'display:none;'; ?>"><?php _e('Remove Favicon', 'volkena'); ?></button>
                                    <p class="description"><?php _e('Upload a 32x32 pixel icon for your site.', 'volkena'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Google Analytics ID', 'volkena'); ?></th>
                                <td>
                                    <input type="text" name="google_analytics" value="<?php echo esc_attr(get_option('volkena_google_analytics')); ?>" class="regular-text" placeholder="G-XXXXXXXXXX" />
                                    <p class="description"><?php _e('Enter your Google Analytics tracking ID.', 'volkena'); ?></p>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Enable Maintenance Mode', 'volkena'); ?></th>
                                <td>
                                    <label>
                                        <input type="checkbox" name="maintenance_mode" value="1" <?php checked(get_option('volkena_maintenance_mode'), 1); ?> />
                                        <?php _e('Enable maintenance mode for non-admin users', 'volkena'); ?>
                                    </label>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <!-- Company Tab -->
                    <div id="company" class="tab-content">
                        <h3><?php _e('Company Information', 'volkena'); ?></h3>

                        <table class="form-table">
                            <tr>
                                <th scope="row"><?php _e('Company Name', 'volkena'); ?></th>
                                <td>
                                    <input type="text" name="company_name" value="<?php echo esc_attr(get_option('volkena_company_name', 'Völkena')); ?>" class="regular-text" />
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Company Tagline', 'volkena'); ?></th>
                                <td>
                                    <input type="text" name="company_tagline" value="<?php echo esc_attr(get_option('volkena_company_tagline', 'German Precision Hearing Solutions')); ?>" class="regular-text" />
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Founded Year', 'volkena'); ?></th>
                                <td>
                                    <input type="number" name="founded_year" value="<?php echo esc_attr(get_option('volkena_founded_year', '2000')); ?>" class="small-text" min="1900" max="<?php echo date('Y'); ?>" />
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('About Company', 'volkena'); ?></th>
                                <td>
                                    <?php
                                    wp_editor(
                                        get_option('volkena_about_company', ''),
                                        'about_company',
                                        array(
                                            'textarea_name' => 'about_company',
                                            'media_buttons' => true,
                                            'textarea_rows' => 10,
                                            'teeny' => false,
                                        )
                                    );
                                    ?>
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Manufacturing Location', 'volkena'); ?></th>
                                <td>
                                    <input type="text" name="manufacturing_location" value="<?php echo esc_attr(get_option('volkena_manufacturing_location', 'China')); ?>" class="regular-text" />
                                </td>
                            </tr>

                            <tr>
                                <th scope="row"><?php _e('Certifications', 'volkena'); ?></th>
                                <td>
                                    <textarea name="certifications" rows="5" cols="50" class="large-text"><?php echo esc_textarea(get_option('volkena_certifications')); ?></textarea>
                                    <p class="description"><?php _e('List your company certifications, one per line.', 'volkena'); ?></p>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div class="submit-section">
                        <?php submit_button(__('Save Settings', 'volkena'), 'primary', 'submit'); ?>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
    .volkena-admin-wrap {
        background: #f1f1f1;
        margin: 20px 0 0 -20px;
        padding: 0;
    }

    .volkena-admin-header {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        padding: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .volkena-logo h2 {
        margin: 0;
        font-size: 2.5rem;
        font-weight: 700;
    }

    .volkena-logo p {
        margin: 5px 0 0 0;
        opacity: 0.9;
        font-size: 1.1rem;
    }

    .volkena-version {
        background: rgba(255,255,255,0.2);
        padding: 10px 15px;
        border-radius: 5px;
        font-size: 0.9rem;
    }

    .volkena-admin-content {
        background: white;
        padding: 0;
    }

    .volkena-admin-tabs .nav-tab-wrapper {
        border-bottom: 1px solid #ccd0d4;
        background: #f9f9f9;
        margin: 0;
        padding: 0 30px;
    }

    .volkena-admin-tabs .nav-tab {
        background: transparent;
        border: none;
        border-bottom: 3px solid transparent;
        color: #666;
        font-weight: 500;
        padding: 15px 20px;
    }

    .volkena-admin-tabs .nav-tab-active,
    .volkena-admin-tabs .nav-tab:hover {
        background: white;
        border-bottom-color: #3498db;
        color: #3498db;
    }

    .tab-content {
        display: none;
        padding: 30px;
    }

    .tab-content.active {
        display: block;
    }

    .tab-content h3 {
        margin-top: 0;
        color: #2c3e50;
        font-size: 1.5rem;
        margin-bottom: 20px;
    }

    .form-table th {
        color: #2c3e50;
        font-weight: 600;
    }

    .submit-section {
        background: #f9f9f9;
        padding: 20px 30px;
        border-top: 1px solid #ccd0d4;
    }

    .logo-preview img,
    .favicon-preview img {
        display: block;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        padding: 5px;
        background: white;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // Tab switching
        $('.nav-tab').click(function(e) {
            e.preventDefault();
            var target = $(this).attr('href');

            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');

            $('.tab-content').removeClass('active');
            $(target).addClass('active');
        });

        // Media uploader for logo
        $('.upload-logo').click(function(e) {
            e.preventDefault();
            var mediaUploader = wp.media({
                title: 'Select Logo',
                button: { text: 'Use this logo' },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#site_logo').val(attachment.id);
                $('.logo-preview').html('<img src="' + attachment.url + '" style="max-width: 200px;" />');
                $('.remove-logo').show();
            });

            mediaUploader.open();
        });

        // Remove logo
        $('.remove-logo').click(function(e) {
            e.preventDefault();
            $('#site_logo').val('');
            $('.logo-preview').empty();
            $(this).hide();
        });

        // Similar handlers for favicon
        $('.upload-favicon').click(function(e) {
            e.preventDefault();
            var mediaUploader = wp.media({
                title: 'Select Favicon',
                button: { text: 'Use this favicon' },
                multiple: false
            });

            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#site_favicon').val(attachment.id);
                $('.favicon-preview').html('<img src="' + attachment.url + '" style="width: 32px; height: 32px;" />');
                $('.remove-favicon').show();
            });

            mediaUploader.open();
        });

        $('.remove-favicon').click(function(e) {
            e.preventDefault();
            $('#site_favicon').val('');
            $('.favicon-preview').empty();
            $(this).hide();
        });
    });
    </script>
    <?php
}

// Save admin settings
function volkena_save_admin_settings() {
    if (!wp_verify_nonce($_POST['volkena_nonce'], 'volkena_admin_settings')) {
        return;
    }

    if (!current_user_can('manage_options')) {
        return;
    }

    // General settings
    if (isset($_POST['site_logo'])) {
        set_theme_mod('custom_logo', intval($_POST['site_logo']));
    }

    if (isset($_POST['site_favicon'])) {
        update_option('volkena_favicon', intval($_POST['site_favicon']));
    }

    if (isset($_POST['google_analytics'])) {
        update_option('volkena_google_analytics', sanitize_text_field($_POST['google_analytics']));
    }

    update_option('volkena_maintenance_mode', isset($_POST['maintenance_mode']) ? 1 : 0);

    // Company settings
    if (isset($_POST['company_name'])) {
        update_option('volkena_company_name', sanitize_text_field($_POST['company_name']));
    }

    if (isset($_POST['company_tagline'])) {
        update_option('volkena_company_tagline', sanitize_text_field($_POST['company_tagline']));
    }

    if (isset($_POST['founded_year'])) {
        update_option('volkena_founded_year', intval($_POST['founded_year']));
    }

    if (isset($_POST['about_company'])) {
        update_option('volkena_about_company', wp_kses_post($_POST['about_company']));
    }

    if (isset($_POST['manufacturing_location'])) {
        update_option('volkena_manufacturing_location', sanitize_text_field($_POST['manufacturing_location']));
    }

    if (isset($_POST['certifications'])) {
        update_option('volkena_certifications', sanitize_textarea_field($_POST['certifications']));
    }
}

// Import/Export page
function volkena_import_export_page() {
    ?>
    <div class="wrap">
        <h1><?php _e('Import/Export Settings', 'volkena'); ?></h1>

        <div class="volkena-import-export">
            <div class="postbox">
                <h3 class="hndle"><?php _e('Export Settings', 'volkena'); ?></h3>
                <div class="inside">
                    <p><?php _e('Export your theme settings, customizer options, and content for backup or migration.', 'volkena'); ?></p>
                    <form method="post" action="">
                        <?php wp_nonce_field('volkena_export', 'export_nonce'); ?>
                        <p>
                            <label>
                                <input type="checkbox" name="export_theme_mods" value="1" checked />
                                <?php _e('Theme Customizer Settings', 'volkena'); ?>
                            </label>
                        </p>
                        <p>
                            <label>
                                <input type="checkbox" name="export_options" value="1" checked />
                                <?php _e('Theme Options', 'volkena'); ?>
                            </label>
                        </p>
                        <p>
                            <label>
                                <input type="checkbox" name="export_products" value="1" />
                                <?php _e('Products', 'volkena'); ?>
                            </label>
                        </p>
                        <p>
                            <label>
                                <input type="checkbox" name="export_services" value="1" />
                                <?php _e('Services', 'volkena'); ?>
                            </label>
                        </p>
                        <?php submit_button(__('Export Settings', 'volkena'), 'secondary', 'export_settings'); ?>
                    </form>
                </div>
            </div>

            <div class="postbox">
                <h3 class="hndle"><?php _e('Import Settings', 'volkena'); ?></h3>
                <div class="inside">
                    <p><?php _e('Import theme settings from a previously exported file.', 'volkena'); ?></p>
                    <form method="post" action="" enctype="multipart/form-data">
                        <?php wp_nonce_field('volkena_import', 'import_nonce'); ?>
                        <p>
                            <input type="file" name="import_file" accept=".json" required />
                        </p>
                        <p>
                            <label>
                                <input type="checkbox" name="overwrite_existing" value="1" />
                                <?php _e('Overwrite existing settings', 'volkena'); ?>
                            </label>
                        </p>
                        <?php submit_button(__('Import Settings', 'volkena'), 'primary', 'import_settings'); ?>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <style>
    .volkena-import-export {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: 20px;
    }

    .volkena-import-export .postbox {
        background: white;
        border: 1px solid #ccd0d4;
        border-radius: 5px;
    }

    .volkena-import-export .hndle {
        background: #f1f1f1;
        padding: 15px 20px;
        margin: 0;
        border-bottom: 1px solid #ccd0d4;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .volkena-import-export .inside {
        padding: 20px;
    }

    @media (max-width: 768px) {
        .volkena-import-export {
            grid-template-columns: 1fr;
        }
    }
    </style>
    <?php

    // Handle export
    if (isset($_POST['export_settings']) && wp_verify_nonce($_POST['export_nonce'], 'volkena_export')) {
        volkena_handle_export();
    }

    // Handle import
    if (isset($_POST['import_settings']) && wp_verify_nonce($_POST['import_nonce'], 'volkena_import')) {
        volkena_handle_import();
    }
}

// Handle settings export
function volkena_handle_export() {
    $export_data = array();

    if (isset($_POST['export_theme_mods'])) {
        $export_data['theme_mods'] = get_theme_mods();
    }

    if (isset($_POST['export_options'])) {
        $export_data['options'] = array();
        $volkena_options = array(
            'volkena_company_name',
            'volkena_company_tagline',
            'volkena_founded_year',
            'volkena_about_company',
            'volkena_manufacturing_location',
            'volkena_certifications',
            'volkena_google_analytics',
            'volkena_maintenance_mode',
            'volkena_favicon',
        );

        foreach ($volkena_options as $option) {
            $export_data['options'][$option] = get_option($option);
        }
    }

    if (isset($_POST['export_products'])) {
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'post_status' => 'any'
        ));

        $export_data['products'] = array();
        foreach ($products as $product) {
            $export_data['products'][] = array(
                'title' => $product->post_title,
                'content' => $product->post_content,
                'excerpt' => $product->post_excerpt,
                'status' => $product->post_status,
                'meta' => get_post_meta($product->ID)
            );
        }
    }

    if (isset($_POST['export_services'])) {
        $services = get_posts(array(
            'post_type' => 'service',
            'posts_per_page' => -1,
            'post_status' => 'any'
        ));

        $export_data['services'] = array();
        foreach ($services as $service) {
            $export_data['services'][] = array(
                'title' => $service->post_title,
                'content' => $service->post_content,
                'excerpt' => $service->post_excerpt,
                'status' => $service->post_status,
                'meta' => get_post_meta($service->ID)
            );
        }
    }

    $export_data['export_date'] = current_time('mysql');
    $export_data['site_url'] = get_site_url();

    $filename = 'volkena-settings-' . date('Y-m-d-H-i-s') . '.json';

    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

    echo json_encode($export_data, JSON_PRETTY_PRINT);
    exit;
}

// Handle settings import
function volkena_handle_import() {
    if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>' . __('Error uploading file.', 'volkena') . '</p></div>';
        });
        return;
    }

    $file_content = file_get_contents($_FILES['import_file']['tmp_name']);
    $import_data = json_decode($file_content, true);

    if (!$import_data) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>' . __('Invalid import file.', 'volkena') . '</p></div>';
        });
        return;
    }

    $overwrite = isset($_POST['overwrite_existing']);

    // Import theme mods
    if (isset($import_data['theme_mods'])) {
        foreach ($import_data['theme_mods'] as $mod => $value) {
            if ($overwrite || !get_theme_mod($mod)) {
                set_theme_mod($mod, $value);
            }
        }
    }

    // Import options
    if (isset($import_data['options'])) {
        foreach ($import_data['options'] as $option => $value) {
            if ($overwrite || !get_option($option)) {
                update_option($option, $value);
            }
        }
    }

    add_action('admin_notices', function() {
        echo '<div class="notice notice-success"><p>' . __('Settings imported successfully!', 'volkena') . '</p></div>';
    });
}

/**
 * ========================================
 * TEMPLATE FLEXIBILITY & HOOKS
 * ========================================
 */

// Add template hooks for customization
function volkena_add_template_hooks() {
    // Header hooks
    add_action('volkena_header_before', 'volkena_output_header_before');
    add_action('volkena_header_after', 'volkena_output_header_after');

    // Content hooks
    add_action('volkena_content_before', 'volkena_output_content_before');
    add_action('volkena_content_after', 'volkena_output_content_after');

    // Footer hooks
    add_action('volkena_footer_before', 'volkena_output_footer_before');
    add_action('volkena_footer_after', 'volkena_output_footer_after');

    // Product hooks
    add_action('volkena_product_before', 'volkena_output_product_before');
    add_action('volkena_product_after', 'volkena_output_product_after');
    add_action('volkena_product_meta', 'volkena_output_product_meta');

    // Service hooks
    add_action('volkena_service_before', 'volkena_output_service_before');
    add_action('volkena_service_after', 'volkena_output_service_after');
    add_action('volkena_service_meta', 'volkena_output_service_meta');
}
add_action('init', 'volkena_add_template_hooks');

// Hook output functions
function volkena_output_header_before() {
    echo '<!-- Header Before Hook -->';
}

function volkena_output_header_after() {
    echo '<!-- Header After Hook -->';
}

function volkena_output_content_before() {
    echo '<!-- Content Before Hook -->';
}

function volkena_output_content_after() {
    echo '<!-- Content After Hook -->';
}

function volkena_output_footer_before() {
    echo '<!-- Footer Before Hook -->';
}

function volkena_output_footer_after() {
    echo '<!-- Footer After Hook -->';
}

function volkena_output_product_before() {
    echo '<!-- Product Before Hook -->';
}

function volkena_output_product_after() {
    echo '<!-- Product After Hook -->';
}

function volkena_output_product_meta() {
    global $post;
    if ($post->post_type === 'product') {
        $price = get_post_meta($post->ID, '_product_price', true);
        $type = get_post_meta($post->ID, '_product_type', true);

        if ($price || $type) {
            echo '<div class="product-meta-hook">';
            if ($type) {
                echo '<span class="product-type">' . esc_html($type) . '</span>';
            }
            if ($price) {
                echo '<span class="product-price">' . esc_html($price) . '</span>';
            }
            echo '</div>';
        }
    }
}

function volkena_output_service_before() {
    echo '<!-- Service Before Hook -->';
}

function volkena_output_service_after() {
    echo '<!-- Service After Hook -->';
}

function volkena_output_service_meta() {
    global $post;
    if ($post->post_type === 'service') {
        $duration = get_post_meta($post->ID, '_service_duration', true);
        $price = get_post_meta($post->ID, '_service_price', true);

        if ($duration || $price) {
            echo '<div class="service-meta-hook">';
            if ($duration) {
                echo '<span class="service-duration">' . esc_html($duration) . '</span>';
            }
            if ($price) {
                echo '<span class="service-price">' . esc_html($price) . '</span>';
            }
            echo '</div>';
        }
    }
}

// Template filters for customization
function volkena_add_template_filters() {
    // Content filters
    add_filter('volkena_hero_title', 'volkena_filter_hero_title');
    add_filter('volkena_hero_subtitle', 'volkena_filter_hero_subtitle');
    add_filter('volkena_company_name', 'volkena_filter_company_name');
    add_filter('volkena_company_tagline', 'volkena_filter_company_tagline');

    // Product filters
    add_filter('volkena_product_title', 'volkena_filter_product_title', 10, 2);
    add_filter('volkena_product_content', 'volkena_filter_product_content', 10, 2);
    add_filter('volkena_product_excerpt', 'volkena_filter_product_excerpt', 10, 2);

    // Service filters
    add_filter('volkena_service_title', 'volkena_filter_service_title', 10, 2);
    add_filter('volkena_service_content', 'volkena_filter_service_content', 10, 2);
    add_filter('volkena_service_excerpt', 'volkena_filter_service_excerpt', 10, 2);
}
add_action('init', 'volkena_add_template_filters');

// Filter functions
function volkena_filter_hero_title($title) {
    return apply_filters('volkena_hero_title_custom', $title);
}

function volkena_filter_hero_subtitle($subtitle) {
    return apply_filters('volkena_hero_subtitle_custom', $subtitle);
}

function volkena_filter_company_name($name) {
    return apply_filters('volkena_company_name_custom', $name);
}

function volkena_filter_company_tagline($tagline) {
    return apply_filters('volkena_company_tagline_custom', $tagline);
}

function volkena_filter_product_title($title, $post_id) {
    return apply_filters('volkena_product_title_custom', $title, $post_id);
}

function volkena_filter_product_content($content, $post_id) {
    return apply_filters('volkena_product_content_custom', $content, $post_id);
}

function volkena_filter_product_excerpt($excerpt, $post_id) {
    return apply_filters('volkena_product_excerpt_custom', $excerpt, $post_id);
}

function volkena_filter_service_title($title, $post_id) {
    return apply_filters('volkena_service_title_custom', $title, $post_id);
}

function volkena_filter_service_content($content, $post_id) {
    return apply_filters('volkena_service_content_custom', $content, $post_id);
}

function volkena_filter_service_excerpt($excerpt, $post_id) {
    return apply_filters('volkena_service_excerpt_custom', $excerpt, $post_id);
}

// Template part loader with hooks
function volkena_get_template_part($slug, $name = null, $args = array()) {
    do_action("volkena_template_part_before_{$slug}", $name, $args);

    if ($name) {
        do_action("volkena_template_part_before_{$slug}_{$name}", $args);
    }

    // Load template part
    get_template_part($slug, $name, $args);

    if ($name) {
        do_action("volkena_template_part_after_{$slug}_{$name}", $args);
    }

    do_action("volkena_template_part_after_{$slug}", $name, $args);
}

// Content wrapper functions with hooks
function volkena_content_wrapper_start() {
    do_action('volkena_content_wrapper_start');
    echo '<div class="content-wrapper">';
}

function volkena_content_wrapper_end() {
    echo '</div>';
    do_action('volkena_content_wrapper_end');
}

// Page builder compatibility functions
function volkena_is_page_builder_active() {
    // Check if Elementor is active and editing
    if (class_exists('\Elementor\Plugin') && \Elementor\Plugin::$instance->editor->is_edit_mode()) {
        return true;
    }

    // Check if Beaver Builder is active and editing
    if (class_exists('FLBuilder') && FLBuilder::is_builder_enabled()) {
        return true;
    }

    // Check if Gutenberg is being used
    if (function_exists('has_blocks') && has_blocks()) {
        return true;
    }

    return false;
}

// Conditional template loading
function volkena_load_template($template_name, $args = array()) {
    $template_path = '';

    // Check for page builder override
    if (volkena_is_page_builder_active()) {
        $builder_template = locate_template("templates/builders/{$template_name}.php");
        if ($builder_template) {
            $template_path = $builder_template;
        }
    }

    // Fallback to default template
    if (!$template_path) {
        $template_path = locate_template("templates/{$template_name}.php");
    }

    // Final fallback
    if (!$template_path) {
        $template_path = locate_template("{$template_name}.php");
    }

    if ($template_path) {
        // Extract args to variables
        if (!empty($args)) {
            extract($args);
        }

        do_action("volkena_before_load_template_{$template_name}", $args);
        include $template_path;
        do_action("volkena_after_load_template_{$template_name}", $args);
    }
}

// Template modification functions for front-end editing
function volkena_modify_template_for_editing($template) {
    if (!is_user_logged_in() || !current_user_can('edit_posts')) {
        return $template;
    }

    // Add editing capabilities to templates
    add_filter('the_title', 'volkena_make_title_editable', 10, 2);
    add_filter('the_content', 'volkena_make_content_editable_filter');
    add_filter('the_excerpt', 'volkena_make_excerpt_editable_filter');

    return $template;
}
add_filter('template_include', 'volkena_modify_template_for_editing');

function volkena_make_title_editable($title, $post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    return volkena_make_content_editable($title, 'title', 'post_title', $post_id);
}

function volkena_make_content_editable_filter($content) {
    $post_id = get_the_ID();
    return volkena_make_content_editable($content, 'content', 'post_content', $post_id);
}

function volkena_make_excerpt_editable_filter($excerpt) {
    $post_id = get_the_ID();
    return volkena_make_content_editable($excerpt, 'excerpt', 'post_excerpt', $post_id);
}
