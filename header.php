<?php
/**
 * The header for our theme
 *
 * @package Volkena
 * @since 1.0.0
 */
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#main"><?php _e('Skip to content', 'volkena'); ?></a>

    <header id="masthead" class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-branding">
                    <?php if (has_custom_logo()) : ?>
                        <div class="site-logo">
                            <?php the_custom_logo(); ?>
                        </div>
                    <?php else : ?>
                        <div class="site-logo">
                            <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                                Völkena<span>hearing</span>
                            </a>
                        </div>
                    <?php endif; ?>

                    <?php if (is_front_page() && is_home()) : ?>
                        <h1 class="site-title screen-reader-text">
                            <a href="<?php echo esc_url(home_url('/')); ?>" rel="home"><?php bloginfo('name'); ?></a>
                        </h1>
                    <?php else : ?>
                        <p class="site-title screen-reader-text">
                            <a href="<?php echo esc_url(home_url('/')); ?>" rel="home"><?php bloginfo('name'); ?></a>
                        </p>
                    <?php endif; ?>

                    <?php
                    $description = get_bloginfo('description', 'display');
                    if ($description || is_customize_preview()) :
                    ?>
                        <p class="site-description screen-reader-text"><?php echo $description; ?></p>
                    <?php endif; ?>
                </div><!-- .site-branding -->

                <nav id="site-navigation" class="main-navigation" role="navigation" aria-label="<?php esc_attr_e('Primary Menu', 'volkena'); ?>">
                    <button class="mobile-menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                        <span></span>
                        <span></span>
                        <span></span>
                        <span class="screen-reader-text"><?php _e('Menu', 'volkena'); ?></span>
                    </button>
                    
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'menu_class'     => 'nav-menu',
                        'container'      => false,
                        'fallback_cb'    => 'volkena_fallback_menu',
                    ));
                    ?>
                    
                    <!-- Language Switcher -->
                    <div class="language-switcher">
                        <?php volkena_language_switcher(); ?>
                    </div>

                    <!-- WooCommerce Cart Icon -->
                    <?php if (class_exists('WooCommerce')) : ?>
                        <?php volkena_header_cart(); ?>
                    <?php endif; ?>
                </nav><!-- #site-navigation -->
            </div><!-- .header-content -->
        </div><!-- .container -->
    </header><!-- #masthead -->

    <!-- Accessibility Controls -->
    <div class="accessibility-controls" id="accessibility-controls">
        <button class="font-size-btn" data-action="decrease" title="<?php esc_attr_e('Decrease font size', 'volkena'); ?>">A-</button>
        <button class="font-size-btn" data-action="reset" title="<?php esc_attr_e('Reset font size', 'volkena'); ?>">A</button>
        <button class="font-size-btn" data-action="increase" title="<?php esc_attr_e('Increase font size', 'volkena'); ?>">A+</button>
        <button class="high-contrast-toggle" title="<?php esc_attr_e('Toggle high contrast', 'volkena'); ?>">
            <?php _e('High Contrast', 'volkena'); ?>
        </button>
    </div>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay">
        <div class="mobile-menu-content">
            <button class="mobile-menu-close">&times;</button>
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_class'     => 'mobile-nav-menu',
                'container'      => false,
                'fallback_cb'    => 'volkena_fallback_menu',
            ));
            ?>
            <div class="mobile-language-switcher">
                <?php volkena_language_switcher(); ?>
            </div>
        </div>
    </div>

<?php
/**
 * Fallback menu function
 */
function volkena_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">' . __('Home', 'volkena') . '</a></li>';
    
    // Products link
    $products_link = get_post_type_archive_link('product');
    if ($products_link) {
        echo '<li><a href="' . esc_url($products_link) . '">' . __('Products', 'volkena') . '</a></li>';
    }
    
    // Services link
    $services_link = get_post_type_archive_link('service');
    if ($services_link) {
        echo '<li><a href="' . esc_url($services_link) . '">' . __('Services', 'volkena') . '</a></li>';
    }
    
    // About page
    $about_page = get_page_by_path('about');
    if ($about_page) {
        echo '<li><a href="' . esc_url(get_permalink($about_page)) . '">' . __('About', 'volkena') . '</a></li>';
    }
    
    // Blog
    if (get_option('page_for_posts')) {
        echo '<li><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '">' . __('Blog', 'volkena') . '</a></li>';
    } else {
        echo '<li><a href="' . esc_url(home_url('/blog/')) . '">' . __('Blog', 'volkena') . '</a></li>';
    }
    
    // Contact page
    $contact_page = get_page_by_path('contact');
    if ($contact_page) {
        echo '<li><a href="' . esc_url(get_permalink($contact_page)) . '">' . __('Contact', 'volkena') . '</a></li>';
    }
    
    echo '</ul>';
}
?>
