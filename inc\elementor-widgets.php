<?php
/**
 * Custom Elementor Widgets for Völkena Theme
 *
 * @package Volkena
 * @since 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Völkena Product Grid Widget
 */
class Volkena_Product_Grid_Widget extends \Elementor\Widget_Base {

    public function get_name() {
        return 'volkena_product_grid';
    }

    public function get_title() {
        return __('Völkena Product Grid', 'volkena');
    }

    public function get_icon() {
        return 'eicon-products';
    }

    public function get_categories() {
        return ['volkena'];
    }

    protected function _register_controls() {
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Content', 'volkena'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'products_count',
            [
                'label' => __('Number of Products', 'volkena'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'default' => 6,
                'min' => 1,
                'max' => 12,
            ]
        );

        $this->add_control(
            'product_type',
            [
                'label' => __('Product Type', 'volkena'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'all',
                'options' => [
                    'all' => __('All Types', 'volkena'),
                    'CIC' => __('CIC (Completely-in-Canal)', 'volkena'),
                    'BTE' => __('BTE (Behind-the-Ear)', 'volkena'),
                    'ITE' => __('ITE (In-the-Ear)', 'volkena'),
                ],
            ]
        );

        $this->add_control(
            'show_filters',
            [
                'label' => __('Show Filter Buttons', 'volkena'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'volkena'),
                'label_off' => __('Hide', 'volkena'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->end_controls_section();

        // Style Section
        $this->start_controls_section(
            'style_section',
            [
                'label' => __('Style', 'volkena'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'columns',
            [
                'label' => __('Columns', 'volkena'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => '3',
                'options' => [
                    '1' => '1',
                    '2' => '2',
                    '3' => '3',
                    '4' => '4',
                ],
                'selectors' => [
                    '{{WRAPPER}} .product-grid' => 'grid-template-columns: repeat({{VALUE}}, 1fr);',
                ],
            ]
        );

        $this->add_responsive_control(
            'gap',
            [
                'label' => __('Gap', 'volkena'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em', 'rem'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 100,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 30,
                ],
                'selectors' => [
                    '{{WRAPPER}} .product-grid' => 'gap: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    protected function render() {
        $settings = $this->get_settings_for_display();
        
        $args = array(
            'post_type' => 'product',
            'posts_per_page' => $settings['products_count'],
            'post_status' => 'publish'
        );

        if ($settings['product_type'] !== 'all') {
            $args['meta_query'] = array(
                array(
                    'key' => '_product_type',
                    'value' => $settings['product_type'],
                    'compare' => '='
                )
            );
        }

        $products = new WP_Query($args);

        if ($products->have_posts()) {
            echo '<div class="volkena-elementor-product-grid">';
            
            if ($settings['show_filters'] === 'yes') {
                echo '<div class="product-filters">';
                echo '<button class="filter-btn active" data-filter="all">' . __('All', 'volkena') . '</button>';
                echo '<button class="filter-btn" data-filter="CIC">' . __('CIC', 'volkena') . '</button>';
                echo '<button class="filter-btn" data-filter="BTE">' . __('BTE', 'volkena') . '</button>';
                echo '<button class="filter-btn" data-filter="ITE">' . __('ITE', 'volkena') . '</button>';
                echo '</div>';
            }
            
            echo '<div class="product-grid">';
            while ($products->have_posts()) {
                $products->the_post();
                $product_type = get_post_meta(get_the_ID(), '_product_type', true);
                $product_price = get_post_meta(get_the_ID(), '_product_price', true);
                
                echo '<div class="product-card" data-type="' . esc_attr($product_type) . '">';
                
                if (has_post_thumbnail()) {
                    echo '<div class="product-image">';
                    echo '<a href="' . get_permalink() . '">';
                    the_post_thumbnail('medium');
                    echo '</a>';
                    echo '</div>';
                } else {
                    echo '<div class="product-image product-placeholder">';
                    echo '<div class="placeholder-icon">🦻</div>';
                    echo '</div>';
                }
                
                echo '<div class="product-content">';
                echo '<h3 class="product-title"><a href="' . get_permalink() . '">' . get_the_title() . '</a></h3>';
                
                if ($product_type) {
                    echo '<div class="product-type">' . esc_html($product_type) . '</div>';
                }
                
                echo '<div class="product-excerpt">' . get_the_excerpt() . '</div>';
                
                if ($product_price) {
                    echo '<div class="product-price">' . esc_html($product_price) . '</div>';
                }
                
                echo '<div class="product-actions">';
                echo '<a href="' . get_permalink() . '" class="btn btn-primary">' . __('Learn More', 'volkena') . '</a>';
                echo '</div>';
                
                echo '</div>'; // .product-content
                echo '</div>'; // .product-card
            }
            echo '</div>'; // .product-grid
            echo '</div>'; // .volkena-elementor-product-grid
        }
        
        wp_reset_postdata();
    }
}

/**
 * Völkena Service Cards Widget
 */
class Volkena_Service_Cards_Widget extends \Elementor\Widget_Base {

    public function get_name() {
        return 'volkena_service_cards';
    }

    public function get_title() {
        return __('Völkena Service Cards', 'volkena');
    }

    public function get_icon() {
        return 'eicon-info-box';
    }

    public function get_categories() {
        return ['volkena'];
    }

    protected function _register_controls() {
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Content', 'volkena'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'services_count',
            [
                'label' => __('Number of Services', 'volkena'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'default' => 3,
                'min' => 1,
                'max' => 6,
            ]
        );

        $this->end_controls_section();
    }

    protected function render() {
        $settings = $this->get_settings_for_display();
        
        $services = new WP_Query(array(
            'post_type' => 'service',
            'posts_per_page' => $settings['services_count'],
            'post_status' => 'publish'
        ));

        if ($services->have_posts()) {
            echo '<div class="volkena-elementor-service-cards">';
            echo '<div class="service-cards-grid">';
            
            while ($services->have_posts()) {
                $services->the_post();
                
                echo '<div class="service-card">';
                
                if (has_post_thumbnail()) {
                    echo '<div class="service-image">';
                    the_post_thumbnail('medium');
                    echo '</div>';
                }
                
                echo '<div class="service-content">';
                echo '<h3 class="service-title">' . get_the_title() . '</h3>';
                echo '<div class="service-excerpt">' . get_the_excerpt() . '</div>';
                echo '<a href="' . get_permalink() . '" class="btn btn-primary">' . __('Learn More', 'volkena') . '</a>';
                echo '</div>';
                
                echo '</div>'; // .service-card
            }
            
            echo '</div>'; // .service-cards-grid
            echo '</div>'; // .volkena-elementor-service-cards
        }
        
        wp_reset_postdata();
    }
}

/**
 * Völkena Contact Info Widget
 */
class Volkena_Contact_Info_Widget extends \Elementor\Widget_Base {

    public function get_name() {
        return 'volkena_contact_info';
    }

    public function get_title() {
        return __('Völkena Contact Info', 'volkena');
    }

    public function get_icon() {
        return 'eicon-call-to-action';
    }

    public function get_categories() {
        return ['volkena'];
    }

    protected function _register_controls() {
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Content', 'volkena'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'show_phone',
            [
                'label' => __('Show Phone', 'volkena'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'volkena'),
                'label_off' => __('Hide', 'volkena'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_email',
            [
                'label' => __('Show Email', 'volkena'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'volkena'),
                'label_off' => __('Hide', 'volkena'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_whatsapp',
            [
                'label' => __('Show WhatsApp', 'volkena'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'volkena'),
                'label_off' => __('Hide', 'volkena'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->end_controls_section();
    }

    protected function render() {
        $settings = $this->get_settings_for_display();
        
        echo '<div class="volkena-elementor-contact-info">';
        
        if ($settings['show_phone'] === 'yes') {
            $phone = get_theme_mod('contact_phone', '+49 123 456 789');
            echo '<div class="contact-item">';
            echo '<span class="contact-icon">📞</span>';
            echo '<div class="contact-details">';
            echo '<strong>' . __('Phone:', 'volkena') . '</strong>';
            echo '<a href="tel:' . esc_attr($phone) . '">' . esc_html($phone) . '</a>';
            echo '</div>';
            echo '</div>';
        }
        
        if ($settings['show_email'] === 'yes') {
            $email = get_theme_mod('contact_email', '<EMAIL>');
            echo '<div class="contact-item">';
            echo '<span class="contact-icon">✉️</span>';
            echo '<div class="contact-details">';
            echo '<strong>' . __('Email:', 'volkena') . '</strong>';
            echo '<a href="mailto:' . esc_attr($email) . '">' . esc_html($email) . '</a>';
            echo '</div>';
            echo '</div>';
        }
        
        if ($settings['show_whatsapp'] === 'yes' && get_theme_mod('whatsapp_number')) {
            $whatsapp = get_theme_mod('whatsapp_number');
            echo '<div class="contact-item">';
            echo '<span class="contact-icon">💬</span>';
            echo '<div class="contact-details">';
            echo '<strong>' . __('WhatsApp:', 'volkena') . '</strong>';
            echo '<a href="https://wa.me/' . esc_attr(str_replace(array('+', ' ', '-'), '', $whatsapp)) . '">' . esc_html($whatsapp) . '</a>';
            echo '</div>';
            echo '</div>';
        }
        
        echo '</div>';
    }
}

// Register widgets
function register_volkena_elementor_widgets() {
    \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new Volkena_Product_Grid_Widget());
    \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new Volkena_Service_Cards_Widget());
    \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new Volkena_Contact_Info_Widget());
}
add_action('elementor/widgets/widgets_registered', 'register_volkena_elementor_widgets');

// Add custom Elementor category
function add_volkena_elementor_widget_categories($elements_manager) {
    $elements_manager->add_category(
        'volkena',
        [
            'title' => __('Völkena Widgets', 'volkena'),
            'icon' => 'fa fa-plug',
        ]
    );
}
add_action('elementor/elements/categories_registered', 'add_volkena_elementor_widget_categories');
