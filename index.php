<?php
/**
 * The main template file
 *
 * @package Volkena
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php if (is_home() && !is_paged()) : ?>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <?php echo esc_html(get_theme_mod('hero_title', __('Premium Hearing Solutions', 'volkena'))); ?>
                    </h1>
                    <p class="hero-subtitle">
                        <?php echo esc_html(get_theme_mod('hero_subtitle', __('German precision engineering meets advanced hearing technology. Discover our range of CIC, BTE, and ITE hearing aids with advanced noise reduction.', 'volkena'))); ?>
                    </p>
                    <div class="hero-buttons">
                        <a href="<?php echo esc_url(get_post_type_archive_link('product')); ?>" class="btn btn-primary">
                            <?php _e('View Products', 'volkena'); ?>
                        </a>
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>" class="btn btn-secondary">
                            <?php _e('Contact Us', 'volkena'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Products Highlight Section -->
        <section class="section">
            <div class="container">
                <div class="section-title">
                    <h2><?php _e('Our Hearing Aid Solutions', 'volkena'); ?></h2>
                    <p><?php _e('Discover our premium range of hearing aids, engineered with German precision and manufactured with the highest quality standards.', 'volkena'); ?></p>
                </div>
                
                <div class="product-grid">
                    <?php
                    $products_query = volkena_get_products(array('posts_per_page' => 3));
                    if ($products_query->have_posts()) :
                        while ($products_query->have_posts()) : $products_query->the_post();
                            $product_type = get_post_meta(get_the_ID(), '_product_type', true);
                            $product_price = get_post_meta(get_the_ID(), '_product_price', true);
                            $product_features = volkena_get_product_features(get_the_ID());
                    ?>
                        <div class="product-card">
                            <div class="product-image">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('medium'); ?>
                                <?php else : ?>
                                    <i class="product-icon">🦻</i>
                                <?php endif; ?>
                            </div>
                            <h3 class="product-title"><?php the_title(); ?></h3>
                            <?php if ($product_type) : ?>
                                <div class="product-type"><?php echo esc_html($product_type); ?></div>
                            <?php endif; ?>
                            <div class="product-description">
                                <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                            </div>
                            <?php if (!empty($product_features)) : ?>
                                <ul class="product-features">
                                    <?php foreach (array_slice($product_features, 0, 3) as $feature) : ?>
                                        <li><?php echo esc_html($feature); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endif; ?>
                            <?php if ($product_price) : ?>
                                <div class="product-price"><?php echo esc_html($product_price); ?></div>
                            <?php endif; ?>
                            <a href="<?php the_permalink(); ?>" class="btn btn-outline">
                                <?php _e('Learn More', 'volkena'); ?>
                            </a>
                        </div>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
                
                <div style="text-align: center; margin-top: 3rem;">
                    <a href="<?php echo esc_url(get_post_type_archive_link('product')); ?>" class="btn btn-primary">
                        <?php _e('View All Products', 'volkena'); ?>
                    </a>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="section" style="background: #f8f9fa;">
            <div class="container">
                <div class="row">
                    <div class="col">
                        <div class="section-title">
                            <h2><?php _e('About Völkena', 'volkena'); ?></h2>
                            <p><?php _e('German Engineering Excellence Since 2000', 'volkena'); ?></p>
                        </div>
                        <div class="about-content">
                            <p><?php _e('Völkena has been at the forefront of hearing aid technology for over two decades. Founded in 2000, we combine German precision engineering with state-of-the-art manufacturing capabilities to deliver premium hearing solutions.', 'volkena'); ?></p>
                            <p><?php _e('Our manufacturing facility in China ensures the highest quality standards while maintaining competitive pricing. We specialize in three main types of hearing aids: CIC (Completely-in-Canal), BTE (Behind-the-Ear), and ITE (In-the-Ear), all featuring advanced noise reduction and volume control technology.', 'volkena'); ?></p>
                            <a href="<?php echo esc_url(get_permalink(get_page_by_path('about'))); ?>" class="btn btn-primary">
                                <?php _e('Learn More About Us', 'volkena'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section class="section">
            <div class="container">
                <div class="section-title">
                    <h2><?php _e('Our Services', 'volkena'); ?></h2>
                    <p><?php _e('Comprehensive hearing care solutions tailored to your needs', 'volkena'); ?></p>
                </div>
                
                <div class="services-grid">
                    <?php
                    $services_query = new WP_Query(array(
                        'post_type' => 'service',
                        'posts_per_page' => 3,
                        'post_status' => 'publish'
                    ));
                    
                    if ($services_query->have_posts()) :
                        while ($services_query->have_posts()) : $services_query->the_post();
                    ?>
                        <div class="service-card">
                            <div class="service-icon">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('thumbnail'); ?>
                                <?php else : ?>
                                    <i>🔧</i>
                                <?php endif; ?>
                            </div>
                            <h3><?php the_title(); ?></h3>
                            <p><?php echo wp_trim_words(get_the_excerpt(), 15); ?></p>
                            <a href="<?php the_permalink(); ?>" class="btn btn-outline">
                                <?php _e('Learn More', 'volkena'); ?>
                            </a>
                        </div>
                    <?php
                        endwhile;
                        wp_reset_postdata();
                    else :
                        // Default services if none are created
                        $default_services = array(
                            array(
                                'title' => __('Hearing Aid Fitting', 'volkena'),
                                'description' => __('Professional fitting services to ensure optimal comfort and performance of your hearing aids.', 'volkena'),
                                'icon' => '👂'
                            ),
                            array(
                                'title' => __('Repair Services', 'volkena'),
                                'description' => __('Expert repair and maintenance services to keep your hearing aids in perfect working condition.', 'volkena'),
                                'icon' => '🔧'
                            ),
                            array(
                                'title' => __('Accessories', 'volkena'),
                                'description' => __('Complete range of hearing aid accessories including batteries, cleaning kits, and protective cases.', 'volkena'),
                                'icon' => '🔋'
                            )
                        );
                        
                        foreach ($default_services as $service) :
                    ?>
                        <div class="service-card">
                            <div class="service-icon">
                                <i><?php echo $service['icon']; ?></i>
                            </div>
                            <h3><?php echo esc_html($service['title']); ?></h3>
                            <p><?php echo esc_html($service['description']); ?></p>
                            <a href="<?php echo esc_url(get_post_type_archive_link('service')); ?>" class="btn btn-outline">
                                <?php _e('Learn More', 'volkena'); ?>
                            </a>
                        </div>
                    <?php
                        endforeach;
                    endif;
                    ?>
                </div>
            </div>
        </section>

        <!-- Contact CTA Section -->
        <section class="section" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;">
            <div class="container">
                <div class="section-title">
                    <h2 style="color: white;"><?php _e('Ready to Improve Your Hearing?', 'volkena'); ?></h2>
                    <p style="color: rgba(255,255,255,0.9);"><?php _e('Contact us today for a consultation and discover the perfect hearing solution for you.', 'volkena'); ?></p>
                </div>
                <div style="text-align: center;">
                    <a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>" class="btn btn-secondary">
                        <?php _e('Contact Us Now', 'volkena'); ?>
                    </a>
                    <?php if (get_theme_mod('whatsapp_number')) : ?>
                        <a href="https://wa.me/<?php echo esc_attr(str_replace(array('+', ' ', '-'), '', get_theme_mod('whatsapp_number'))); ?>" class="btn btn-outline" style="margin-left: 1rem; border-color: white; color: white;">
                            <?php _e('WhatsApp Us', 'volkena'); ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- Blog Posts Section -->
    <?php if (have_posts()) : ?>
        <section class="section">
            <div class="container">
                <?php if (is_home() && !is_paged()) : ?>
                    <div class="section-title">
                        <h2><?php _e('Latest News & Articles', 'volkena'); ?></h2>
                        <p><?php _e('Stay informed about hearing health and the latest developments in hearing aid technology.', 'volkena'); ?></p>
                    </div>
                <?php endif; ?>
                
                <div class="posts-grid">
                    <?php while (have_posts()) : the_post(); ?>
                        <article id="post-<?php the_ID(); ?>" <?php post_class('post-card'); ?>>
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-thumbnail">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-content">
                                <div class="post-meta">
                                    <time datetime="<?php echo get_the_date('c'); ?>">
                                        <?php echo get_the_date(); ?>
                                    </time>
                                    <span class="post-author">
                                        <?php _e('by', 'volkena'); ?> <?php the_author(); ?>
                                    </span>
                                </div>
                                
                                <h3 class="post-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h3>
                                
                                <div class="post-excerpt">
                                    <?php the_excerpt(); ?>
                                </div>
                                
                                <a href="<?php the_permalink(); ?>" class="read-more">
                                    <?php _e('Read More', 'volkena'); ?>
                                </a>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>
                
                <?php
                the_posts_pagination(array(
                    'prev_text' => __('Previous', 'volkena'),
                    'next_text' => __('Next', 'volkena'),
                ));
                ?>
            </div>
        </section>
    <?php else : ?>
        <section class="section">
            <div class="container">
                <div class="no-posts">
                    <h2><?php _e('Nothing Found', 'volkena'); ?></h2>
                    <p><?php _e('It looks like nothing was found at this location. Maybe try a search?', 'volkena'); ?></p>
                    <?php get_search_form(); ?>
                </div>
            </div>
        </section>
    <?php endif; ?>
</main>

<?php get_footer(); ?>
