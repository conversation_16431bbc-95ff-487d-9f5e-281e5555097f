<?php
/**
 * Template for Contact page
 *
 * @package Volkena
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php while (have_posts()) : the_post(); ?>
    
    <!-- Page Header -->
    <section class="page-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 100px 0 60px;">
        <div class="container">
            <div class="page-header-content">
                <h1 class="page-title"><?php _e('Contact Us', 'volkena'); ?></h1>
                <p class="page-description"><?php _e('Get in touch with our hearing specialists. We\'re here to help you find the perfect hearing solution.', 'volkena'); ?></p>
            </div>
        </div>
    </section>

    <!-- Contact Information -->
    <section class="section contact-info">
        <div class="container">
            <div class="contact-info-grid">
                <div class="contact-card">
                    <div class="contact-icon">📍</div>
                    <h3><?php _e('Visit Us', 'volkena'); ?></h3>
                    <p><?php _e('Völkena Headquarters', 'volkena'); ?><br>
                    <?php _e('Musterstraße 123', 'volkena'); ?><br>
                    <?php _e('10115 Berlin, Germany', 'volkena'); ?></p>
                </div>
                
                <div class="contact-card">
                    <div class="contact-icon">📞</div>
                    <h3><?php _e('Call Us', 'volkena'); ?></h3>
                    <p><a href="tel:+*************">+49 30 123 456 789</a><br>
                    <?php _e('Monday - Friday: 9:00 - 18:00', 'volkena'); ?><br>
                    <?php _e('Saturday: 9:00 - 14:00', 'volkena'); ?></p>
                </div>
                
                <div class="contact-card">
                    <div class="contact-icon">✉️</div>
                    <h3><?php _e('Email Us', 'volkena'); ?></h3>
                    <p><a href="mailto:<EMAIL>"><EMAIL></a><br>
                    <a href="mailto:<EMAIL>"><EMAIL></a><br>
                    <?php _e('We respond within 24 hours', 'volkena'); ?></p>
                </div>
                
                <?php if (get_theme_mod('whatsapp_number')) : ?>
                <div class="contact-card">
                    <div class="contact-icon">💬</div>
                    <h3><?php _e('WhatsApp', 'volkena'); ?></h3>
                    <p><a href="https://wa.me/<?php echo esc_attr(str_replace(array('+', ' ', '-'), '', get_theme_mod('whatsapp_number'))); ?>" target="_blank"><?php echo esc_html(get_theme_mod('whatsapp_number')); ?></a><br>
                    <?php _e('Quick support and inquiries', 'volkena'); ?><br>
                    <?php _e('Available during business hours', 'volkena'); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Contact Form and Map -->
    <section class="section contact-form-section" style="background: #f8f9fa;">
        <div class="container">
            <div class="contact-layout">
                <div class="contact-form-container">
                    <div class="form-header">
                        <h2><?php _e('Send Us a Message', 'volkena'); ?></h2>
                        <p><?php _e('Fill out the form below and we\'ll get back to you as soon as possible.', 'volkena'); ?></p>
                    </div>
                    
                    <form id="contact-form" class="contact-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="contact-name"><?php _e('Name', 'volkena'); ?> *</label>
                                <input type="text" id="contact-name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="contact-email"><?php _e('Email', 'volkena'); ?> *</label>
                                <input type="email" id="contact-email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="contact-phone"><?php _e('Phone', 'volkena'); ?></label>
                                <input type="tel" id="contact-phone" name="phone">
                            </div>
                            <div class="form-group">
                                <label for="contact-subject"><?php _e('Subject', 'volkena'); ?></label>
                                <select id="contact-subject" name="subject">
                                    <option value=""><?php _e('Select a subject', 'volkena'); ?></option>
                                    <option value="product-inquiry"><?php _e('Product Inquiry', 'volkena'); ?></option>
                                    <option value="fitting-appointment"><?php _e('Fitting Appointment', 'volkena'); ?></option>
                                    <option value="repair-service"><?php _e('Repair Service', 'volkena'); ?></option>
                                    <option value="technical-support"><?php _e('Technical Support', 'volkena'); ?></option>
                                    <option value="general-inquiry"><?php _e('General Inquiry', 'volkena'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="contact-message"><?php _e('Message', 'volkena'); ?> *</label>
                            <textarea id="contact-message" name="message" rows="6" required placeholder="<?php _e('Please describe your hearing needs, questions, or how we can help you...', 'volkena'); ?>"></textarea>
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="newsletter" value="1">
                                <span class="checkmark"></span>
                                <?php _e('I would like to receive updates about new products and hearing health tips', 'volkena'); ?>
                            </label>
                        </div>
                        
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="privacy" value="1" required>
                                <span class="checkmark"></span>
                                <?php _e('I agree to the privacy policy and terms of service', 'volkena'); ?> *
                            </label>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary btn-large">
                                <?php _e('Send Message', 'volkena'); ?>
                            </button>
                        </div>
                    </form>
                    
                    <div id="form-message" class="form-message"></div>
                </div>
                
                <div class="map-container">
                    <div class="map-header">
                        <h3><?php _e('Find Us', 'volkena'); ?></h3>
                        <p><?php _e('Visit our headquarters in Berlin for personalized consultations and hearing aid fittings.', 'volkena'); ?></p>
                    </div>
                    
                    <!-- Map Placeholder - In a real implementation, you would integrate with Google Maps or similar -->
                    <div class="map-placeholder">
                        <div class="map-icon">🗺️</div>
                        <p><?php _e('Interactive Map', 'volkena'); ?></p>
                        <p class="map-address">
                            <?php _e('Völkena Headquarters', 'volkena'); ?><br>
                            <?php _e('Musterstraße 123', 'volkena'); ?><br>
                            <?php _e('10115 Berlin, Germany', 'volkena'); ?>
                        </p>
                        <a href="https://maps.google.com/?q=Berlin,Germany" target="_blank" class="btn btn-outline btn-sm">
                            <?php _e('Open in Google Maps', 'volkena'); ?>
                        </a>
                    </div>
                    
                    <div class="location-details">
                        <h4><?php _e('Getting Here', 'volkena'); ?></h4>
                        <ul class="location-info">
                            <li><strong><?php _e('By Public Transport:', 'volkena'); ?></strong> <?php _e('U-Bahn Line U6, Station Friedrichstraße', 'volkena'); ?></li>
                            <li><strong><?php _e('By Car:', 'volkena'); ?></strong> <?php _e('Parking available on-site', 'volkena'); ?></li>
                            <li><strong><?php _e('Accessibility:', 'volkena'); ?></strong> <?php _e('Wheelchair accessible entrance', 'volkena'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="section faq-section">
        <div class="container">
            <div class="section-title">
                <h2><?php _e('Frequently Asked Questions', 'volkena'); ?></h2>
                <p><?php _e('Quick answers to common questions about our products and services', 'volkena'); ?></p>
            </div>
            
            <div class="faq-grid">
                <div class="faq-item">
                    <h3><?php _e('How do I choose the right hearing aid?', 'volkena'); ?></h3>
                    <p><?php _e('The best hearing aid depends on your degree of hearing loss, lifestyle, and personal preferences. Our specialists will conduct a thorough assessment and recommend the most suitable option for you.', 'volkena'); ?></p>
                </div>
                
                <div class="faq-item">
                    <h3><?php _e('Do you offer fitting services?', 'volkena'); ?></h3>
                    <p><?php _e('Yes, we provide professional fitting services to ensure your hearing aid is perfectly adjusted for optimal comfort and performance. Follow-up appointments are also available.', 'volkena'); ?></p>
                </div>
                
                <div class="faq-item">
                    <h3><?php _e('What is your warranty policy?', 'volkena'); ?></h3>
                    <p><?php _e('All Völkena hearing aids come with a comprehensive warranty covering manufacturing defects and performance issues. Extended warranty options are also available.', 'volkena'); ?></p>
                </div>
                
                <div class="faq-item">
                    <h3><?php _e('How long do hearing aid batteries last?', 'volkena'); ?></h3>
                    <p><?php _e('Battery life varies by model and usage, but typically ranges from 3-14 days. We provide guidance on battery care and offer high-quality replacement batteries.', 'volkena'); ?></p>
                </div>
                
                <div class="faq-item">
                    <h3><?php _e('Do you provide repair services?', 'volkena'); ?></h3>
                    <p><?php _e('Yes, we offer comprehensive repair services for all Völkena hearing aids. Our expert technicians can diagnose and fix most issues quickly and efficiently.', 'volkena'); ?></p>
                </div>
                
                <div class="faq-item">
                    <h3><?php _e('Can I try before I buy?', 'volkena'); ?></h3>
                    <p><?php _e('We offer trial periods for our hearing aids so you can experience the difference in your daily life before making a final decision. Contact us to learn more about our trial program.', 'volkena'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Contact Options -->
    <section class="section quick-contact" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;">
        <div class="container">
            <div class="quick-contact-content">
                <h2 style="color: white;"><?php _e('Need Immediate Assistance?', 'volkena'); ?></h2>
                <p style="color: rgba(255,255,255,0.9);"><?php _e('Our hearing specialists are ready to help you with any questions or concerns.', 'volkena'); ?></p>
                
                <div class="quick-contact-options">
                    <a href="tel:+*************" class="quick-contact-btn">
                        <span class="contact-icon">📞</span>
                        <span class="contact-text">
                            <strong><?php _e('Call Now', 'volkena'); ?></strong>
                            <small>+49 30 123 456 789</small>
                        </span>
                    </a>
                    
                    <?php if (get_theme_mod('whatsapp_number')) : ?>
                    <a href="https://wa.me/<?php echo esc_attr(str_replace(array('+', ' ', '-'), '', get_theme_mod('whatsapp_number'))); ?>" class="quick-contact-btn" target="_blank">
                        <span class="contact-icon">💬</span>
                        <span class="contact-text">
                            <strong><?php _e('WhatsApp', 'volkena'); ?></strong>
                            <small><?php _e('Instant messaging', 'volkena'); ?></small>
                        </span>
                    </a>
                    <?php endif; ?>
                    
                    <a href="mailto:<EMAIL>" class="quick-contact-btn">
                        <span class="contact-icon">✉️</span>
                        <span class="contact-text">
                            <strong><?php _e('Email Us', 'volkena'); ?></strong>
                            <small><EMAIL></small>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php endwhile; ?>
</main>

<?php get_footer(); ?>
