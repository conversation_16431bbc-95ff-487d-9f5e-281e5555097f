<?php
/**
 * The template for displaying search results pages
 *
 * @package Volkena
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <!-- Search Header -->
    <section class="search-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 100px 0 60px;">
        <div class="container">
            <div class="search-header-content">
                <?php if (have_posts()) : ?>
                    <h1 class="page-title">
                        <?php printf(__('Search Results for: %s', 'volkena'), '<span class="search-term">' . get_search_query() . '</span>'); ?>
                    </h1>
                    <p class="search-description">
                        <?php printf(_n('Found %d result', 'Found %d results', $wp_query->found_posts, 'volkena'), $wp_query->found_posts); ?>
                    </p>
                <?php else : ?>
                    <h1 class="page-title">
                        <?php printf(__('No Results for: %s', 'volkena'), '<span class="search-term">' . get_search_query() . '</span>'); ?>
                    </h1>
                    <p class="search-description">
                        <?php _e('Sorry, no results were found. Please try a different search term.', 'volkena'); ?>
                    </p>
                <?php endif; ?>
                
                <!-- Search Form -->
                <div class="search-form-container">
                    <?php get_search_form(); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Results -->
    <section class="section search-results">
        <div class="container">
            <?php if (have_posts()) : ?>
                <div class="search-results-grid">
                    <?php while (have_posts()) : the_post(); ?>
                        <article id="post-<?php the_ID(); ?>" <?php post_class('search-result-item'); ?>>
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="result-thumbnail">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('medium'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <div class="result-content">
                                <div class="result-meta">
                                    <span class="result-type">
                                        <?php 
                                        $post_type = get_post_type();
                                        switch($post_type) {
                                            case 'product':
                                                _e('Product', 'volkena');
                                                break;
                                            case 'service':
                                                _e('Service', 'volkena');
                                                break;
                                            case 'page':
                                                _e('Page', 'volkena');
                                                break;
                                            default:
                                                _e('Article', 'volkena');
                                        }
                                        ?>
                                    </span>
                                    <?php if (get_post_type() === 'post') : ?>
                                        <span class="result-date">
                                            <time datetime="<?php echo get_the_date('c'); ?>">
                                                <?php echo get_the_date(); ?>
                                            </time>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <h2 class="result-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h2>
                                
                                <div class="result-excerpt">
                                    <?php 
                                    $excerpt = get_the_excerpt();
                                    $search_query = get_search_query();
                                    if ($search_query && $excerpt) {
                                        // Highlight search terms in excerpt
                                        $highlighted = preg_replace('/(' . preg_quote($search_query, '/') . ')/i', '<mark>$1</mark>', $excerpt);
                                        echo $highlighted;
                                    } else {
                                        echo $excerpt;
                                    }
                                    ?>
                                </div>
                                
                                <?php if (get_post_type() === 'product') : ?>
                                    <?php 
                                    $product_type = get_post_meta(get_the_ID(), '_product_type', true);
                                    $product_price = get_post_meta(get_the_ID(), '_product_price', true);
                                    ?>
                                    <div class="result-product-info">
                                        <?php if ($product_type) : ?>
                                            <span class="product-type-badge"><?php echo esc_html($product_type); ?></span>
                                        <?php endif; ?>
                                        <?php if ($product_price) : ?>
                                            <span class="product-price"><?php echo esc_html($product_price); ?></span>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="result-actions">
                                    <a href="<?php the_permalink(); ?>" class="btn btn-outline btn-sm">
                                        <?php _e('Read More', 'volkena'); ?>
                                    </a>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>
                
                <!-- Pagination -->
                <div class="pagination-wrapper">
                    <?php
                    the_posts_pagination(array(
                        'prev_text' => __('← Previous', 'volkena'),
                        'next_text' => __('Next →', 'volkena'),
                        'before_page_number' => '<span class="screen-reader-text">' . __('Page', 'volkena') . ' </span>',
                    ));
                    ?>
                </div>
                
            <?php else : ?>
                <div class="no-results">
                    <div class="no-results-content">
                        <div class="no-results-icon">🔍</div>
                        <h2><?php _e('No Results Found', 'volkena'); ?></h2>
                        <p><?php _e('We couldn\'t find any content matching your search. Here are some suggestions:', 'volkena'); ?></p>
                        
                        <div class="search-suggestions">
                            <ul>
                                <li><?php _e('Check your spelling and try again', 'volkena'); ?></li>
                                <li><?php _e('Try using different or more general keywords', 'volkena'); ?></li>
                                <li><?php _e('Browse our products and services below', 'volkena'); ?></li>
                            </ul>
                        </div>
                        
                        <div class="alternative-actions">
                            <a href="<?php echo esc_url(get_post_type_archive_link('product')); ?>" class="btn btn-primary">
                                <?php _e('Browse Products', 'volkena'); ?>
                            </a>
                            <a href="<?php echo esc_url(get_post_type_archive_link('service')); ?>" class="btn btn-outline">
                                <?php _e('View Services', 'volkena'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Popular Content -->
    <?php
    $popular_posts = new WP_Query(array(
        'posts_per_page' => 3,
        'meta_key' => 'post_views_count',
        'orderby' => 'meta_value_num',
        'order' => 'DESC'
    ));
    
    if (!$popular_posts->have_posts()) {
        // Fallback to recent posts if no view counts
        $popular_posts = new WP_Query(array(
            'posts_per_page' => 3,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
    }
    
    if ($popular_posts->have_posts()) :
    ?>
    <section class="section popular-content" style="background: #f8f9fa;">
        <div class="container">
            <div class="section-title">
                <h2><?php _e('Popular Content', 'volkena'); ?></h2>
                <p><?php _e('Discover our most popular articles and resources', 'volkena'); ?></p>
            </div>
            
            <div class="popular-content-grid">
                <?php while ($popular_posts->have_posts()) : $popular_posts->the_post(); ?>
                    <article class="popular-item">
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="popular-thumbnail">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <div class="popular-content">
                            <h3 class="popular-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h3>
                            <div class="popular-excerpt">
                                <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                            </div>
                            <a href="<?php the_permalink(); ?>" class="read-more">
                                <?php _e('Read More', 'volkena'); ?> →
                            </a>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>
        </div>
    </section>
    <?php 
    wp_reset_postdata();
    endif; 
    ?>
</main>

<style>
.search-header-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.search-term {
    color: #3498db;
    font-weight: 600;
}

.search-form-container {
    margin-top: 2rem;
}

.search-results-grid {
    display: grid;
    gap: 2rem;
    margin-top: 2rem;
}

.search-result-item {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1.5rem;
    transition: transform 0.3s ease;
}

.search-result-item:hover {
    transform: translateY(-2px);
}

.result-thumbnail {
    height: 150px;
    overflow: hidden;
}

.result-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.result-content {
    padding: 1.5rem 1.5rem 1.5rem 0;
}

.result-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.result-type {
    background: #3498db;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.result-title a {
    color: #2c3e50;
    text-decoration: none;
}

.result-title a:hover {
    color: #3498db;
}

.result-excerpt {
    color: #7f8c8d;
    margin: 1rem 0;
    line-height: 1.6;
}

.result-excerpt mark {
    background: #fff3cd;
    padding: 0.1em 0.2em;
    border-radius: 2px;
}

.result-product-info {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
}

.product-type-badge {
    background: #ecf0f1;
    color: #2c3e50;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.product-price {
    color: #27ae60;
    font-weight: 600;
}

.no-results {
    text-align: center;
    padding: 4rem 0;
}

.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.search-suggestions {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin: 2rem 0;
    text-align: left;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.search-suggestions ul {
    margin: 0;
    padding-left: 1.5rem;
}

.search-suggestions li {
    margin-bottom: 0.5rem;
    color: #555;
}

.alternative-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.popular-content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.popular-item {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.popular-item:hover {
    transform: translateY(-5px);
}

.popular-thumbnail {
    height: 200px;
    overflow: hidden;
}

.popular-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-content {
    padding: 1.5rem;
}

.popular-title a {
    color: #2c3e50;
    text-decoration: none;
}

.popular-title a:hover {
    color: #3498db;
}

.popular-excerpt {
    color: #7f8c8d;
    margin: 1rem 0;
}

@media (max-width: 768px) {
    .search-result-item {
        grid-template-columns: 1fr;
    }
    
    .result-thumbnail {
        height: 200px;
    }
    
    .result-content {
        padding: 1.5rem;
    }
    
    .alternative-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .alternative-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>

<?php get_footer(); ?>
