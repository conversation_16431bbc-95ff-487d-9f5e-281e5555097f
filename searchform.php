<?php
/**
 * Search form template
 *
 * @package Volkena
 * @since 1.0.0
 */
?>

<form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
    <div class="search-form-wrapper">
        <label for="search-field" class="screen-reader-text">
            <?php _e('Search for:', 'volkena'); ?>
        </label>
        <input 
            type="search" 
            id="search-field" 
            class="search-field" 
            placeholder="<?php _e('Search hearing aids, services, articles...', 'volkena'); ?>" 
            value="<?php echo get_search_query(); ?>" 
            name="s" 
            required
        />
        <button type="submit" class="search-submit">
            <span class="search-icon">🔍</span>
            <span class="search-text"><?php _e('Search', 'volkena'); ?></span>
        </button>
    </div>
    
    <!-- Search suggestions (optional) -->
    <div class="search-suggestions-dropdown" style="display: none;">
        <div class="suggestions-header">
            <?php _e('Popular searches:', 'volkena'); ?>
        </div>
        <div class="suggestions-list">
            <a href="<?php echo esc_url(add_query_arg('s', 'CIC hearing aids', home_url('/'))); ?>" class="suggestion-item">
                <?php _e('CIC hearing aids', 'volkena'); ?>
            </a>
            <a href="<?php echo esc_url(add_query_arg('s', 'BTE hearing aids', home_url('/'))); ?>" class="suggestion-item">
                <?php _e('BTE hearing aids', 'volkena'); ?>
            </a>
            <a href="<?php echo esc_url(add_query_arg('s', 'hearing aid fitting', home_url('/'))); ?>" class="suggestion-item">
                <?php _e('Hearing aid fitting', 'volkena'); ?>
            </a>
            <a href="<?php echo esc_url(add_query_arg('s', 'repair service', home_url('/'))); ?>" class="suggestion-item">
                <?php _e('Repair service', 'volkena'); ?>
            </a>
        </div>
    </div>
</form>

<style>
.search-form {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-form-wrapper {
    display: flex;
    background: white;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.search-form-wrapper:focus-within {
    box-shadow: 0 5px 20px rgba(52, 152, 219, 0.3);
    transform: translateY(-2px);
}

.search-field {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    font-size: 1rem;
    color: #2c3e50;
    outline: none;
}

.search-field::placeholder {
    color: #bdc3c7;
}

.search-submit {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-submit:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateX(-2px);
}

.search-icon {
    font-size: 1.2rem;
}

.search-suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 0.5rem;
    z-index: 1000;
    overflow: hidden;
}

.suggestions-header {
    padding: 1rem 1.5rem 0.5rem;
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 600;
}

.suggestions-list {
    display: flex;
    flex-direction: column;
}

.suggestion-item {
    padding: 0.75rem 1.5rem;
    color: #2c3e50;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid #ecf0f1;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background: #f8f9fa;
    color: #3498db;
    padding-left: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-form {
        max-width: 100%;
    }
    
    .search-field {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .search-submit {
        padding: 0.75rem 1.5rem;
    }
    
    .search-text {
        display: none;
    }
}

@media (max-width: 480px) {
    .search-form-wrapper {
        border-radius: 25px;
    }
    
    .search-field {
        padding: 0.75rem 1rem;
    }
    
    .search-submit {
        padding: 0.75rem 1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .search-form-wrapper {
        background: #2d2d2d;
        border: 1px solid #404040;
    }
    
    .search-field {
        color: #e0e0e0;
    }
    
    .search-field::placeholder {
        color: #888;
    }
    
    .search-suggestions-dropdown {
        background: #2d2d2d;
        border: 1px solid #404040;
    }
    
    .suggestions-header {
        color: #bbb;
    }
    
    .suggestion-item {
        color: #e0e0e0;
        border-bottom-color: #404040;
    }
    
    .suggestion-item:hover {
        background: #404040;
        color: #3498db;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .search-form-wrapper {
        border: 2px solid #000;
    }
    
    .search-submit {
        background: #000;
        color: #fff;
    }
    
    .search-field {
        color: #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    .search-form-wrapper,
    .search-submit,
    .suggestion-item {
        transition: none;
    }
    
    .search-form-wrapper:focus-within {
        transform: none;
    }
    
    .search-submit:hover {
        transform: none;
    }
}
</style>

<script>
// Enhanced search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchField = document.querySelector('.search-field');
    const suggestionsDropdown = document.querySelector('.search-suggestions-dropdown');
    
    if (searchField && suggestionsDropdown) {
        // Show suggestions on focus
        searchField.addEventListener('focus', function() {
            if (this.value.length === 0) {
                suggestionsDropdown.style.display = 'block';
            }
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-form')) {
                suggestionsDropdown.style.display = 'none';
            }
        });
        
        // Hide suggestions when typing
        searchField.addEventListener('input', function() {
            if (this.value.length > 0) {
                suggestionsDropdown.style.display = 'none';
            } else {
                suggestionsDropdown.style.display = 'block';
            }
        });
        
        // Handle suggestion clicks
        const suggestionItems = document.querySelectorAll('.suggestion-item');
        suggestionItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const searchTerm = this.textContent.trim();
                searchField.value = searchTerm;
                suggestionsDropdown.style.display = 'none';
                // Submit the form
                this.closest('.search-form').submit();
            });
        });
    }
    
    // Add search analytics tracking
    const searchForm = document.querySelector('.search-form');
    if (searchForm) {
        searchForm.addEventListener('submit', function() {
            const searchTerm = searchField.value.trim();
            if (searchTerm && typeof trackEvent === 'function') {
                trackEvent('Search', 'Submit', searchTerm);
            }
        });
    }
});
</script>
