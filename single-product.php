<?php
/**
 * The template for displaying single product
 *
 * @package Volkena
 * @since 1.0.0
 */

get_header(); ?>

<?php do_action('volkena_content_before'); ?>

<main id="main" class="site-main">
    <?php while (have_posts()) : the_post();
        $product_type = get_post_meta(get_the_ID(), '_product_type', true);
        $product_price = get_post_meta(get_the_ID(), '_product_price', true);
        $product_features = volkena_get_product_features(get_the_ID());
        $product_specifications = get_post_meta(get_the_ID(), '_product_specifications', true);

        do_action('volkena_product_before');
    ?>
    
    <!-- Breadcrumb -->
    <section class="breadcrumb-section" style="background: #f8f9fa; padding: 1rem 0;">
        <div class="container">
            <nav class="breadcrumb">
                <a href="<?php echo esc_url(home_url('/')); ?>"><?php _e('Home', 'volkena'); ?></a>
                <span class="separator">›</span>
                <a href="<?php echo esc_url(get_post_type_archive_link('product')); ?>"><?php _e('Products', 'volkena'); ?></a>
                <span class="separator">›</span>
                <span class="current"><?php the_title(); ?></span>
            </nav>
        </div>
    </section>

    <!-- Product Details -->
    <section class="section product-details">
        <div class="container">
            <div class="product-layout">
                <div class="product-images">
                    <div class="main-image">
                        <?php if (has_post_thumbnail()) : ?>
                            <?php the_post_thumbnail('large', array('class' => 'product-main-image')); ?>
                        <?php else : ?>
                            <div class="product-placeholder-large">
                                <i class="product-icon">🦻</i>
                                <p><?php _e('Product Image', 'volkena'); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Additional images could be added here -->
                    <div class="image-gallery">
                        <!-- Placeholder for additional product images -->
                    </div>
                </div>
                
                <div class="product-info">
                    <div class="product-header">
                        <h1 class="product-title"><?php the_title(); ?></h1>
                        
                        <?php if ($product_type) : ?>
                            <div class="product-type-badge">
                                <?php 
                                switch($product_type) {
                                    case 'CIC':
                                        _e('Completely-in-Canal', 'volkena');
                                        break;
                                    case 'BTE':
                                        _e('Behind-the-Ear', 'volkena');
                                        break;
                                    case 'ITE':
                                        _e('In-the-Ear', 'volkena');
                                        break;
                                    default:
                                        echo esc_html($product_type);
                                }
                                ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($product_price) : ?>
                            <div class="product-price-large"><?php echo esc_html($product_price); ?></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-description">
                        <?php the_content(); ?>
                    </div>
                    
                    <?php if (!empty($product_features)) : ?>
                        <div class="product-features-section">
                            <h3><?php _e('Key Features', 'volkena'); ?></h3>
                            <ul class="product-features-list">
                                <?php foreach ($product_features as $feature) : ?>
                                    <li><?php echo esc_html($feature); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <div class="product-actions">
                        <button class="btn btn-primary btn-large contact-btn">
                            <?php _e('Request Information', 'volkena'); ?>
                        </button>
                        <button class="btn btn-outline btn-large compare-btn" data-product-id="<?php echo get_the_ID(); ?>">
                            <?php _e('Add to Compare', 'volkena'); ?>
                        </button>
                        <?php if (get_theme_mod('whatsapp_number')) : ?>
                            <a href="https://wa.me/<?php echo esc_attr(str_replace(array('+', ' ', '-'), '', get_theme_mod('whatsapp_number'))); ?>?text=<?php echo urlencode(__('I\'m interested in', 'volkena') . ' ' . get_the_title()); ?>" class="btn btn-secondary btn-large" target="_blank">
                                <?php _e('WhatsApp Inquiry', 'volkena'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Specifications -->
    <?php if ($product_specifications) : ?>
    <section class="section specifications-section" style="background: #f8f9fa;">
        <div class="container">
            <div class="specifications-content">
                <h2><?php _e('Technical Specifications', 'volkena'); ?></h2>
                <div class="specifications-text">
                    <?php echo wp_kses_post(wpautop($product_specifications)); ?>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Product Benefits -->
    <section class="section benefits-section">
        <div class="container">
            <div class="section-title">
                <h2><?php _e('Why Choose This Hearing Aid?', 'volkena'); ?></h2>
                <p><?php _e('Experience the benefits of German engineering and advanced hearing technology', 'volkena'); ?></p>
            </div>
            
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">🇩🇪</div>
                    <h3><?php _e('German Engineering', 'volkena'); ?></h3>
                    <p><?php _e('Precision-crafted with meticulous attention to detail and quality.', 'volkena'); ?></p>
                </div>
                
                <div class="benefit-card">
                    <div class="benefit-icon">🔊</div>
                    <h3><?php _e('Advanced Sound Processing', 'volkena'); ?></h3>
                    <p><?php _e('Crystal-clear sound quality with intelligent noise reduction technology.', 'volkena'); ?></p>
                </div>
                
                <div class="benefit-card">
                    <div class="benefit-icon">⚡</div>
                    <h3><?php _e('Long Battery Life', 'volkena'); ?></h3>
                    <p><?php _e('Extended usage time with efficient power management systems.', 'volkena'); ?></p>
                </div>
                
                <div class="benefit-card">
                    <div class="benefit-icon">🛡️</div>
                    <h3><?php _e('Durable Construction', 'volkena'); ?></h3>
                    <p><?php _e('Built to last with high-quality materials and robust design.', 'volkena'); ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products -->
    <?php
    $related_products = new WP_Query(array(
        'post_type' => 'product',
        'posts_per_page' => 3,
        'post__not_in' => array(get_the_ID()),
        'meta_query' => array(
            array(
                'key' => '_product_type',
                'value' => $product_type,
                'compare' => '='
            )
        )
    ));
    
    if ($related_products->have_posts()) :
    ?>
    <section class="section related-products" style="background: #f8f9fa;">
        <div class="container">
            <div class="section-title">
                <h2><?php _e('Related Products', 'volkena'); ?></h2>
                <p><?php _e('Explore other hearing aids in the same category', 'volkena'); ?></p>
            </div>
            
            <div class="related-products-grid">
                <?php while ($related_products->have_posts()) : $related_products->the_post(); 
                    $related_price = get_post_meta(get_the_ID(), '_product_price', true);
                    $related_features = volkena_get_product_features(get_the_ID());
                ?>
                    <div class="related-product-card">
                        <div class="related-product-image">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium'); ?>
                                </a>
                            <?php else : ?>
                                <div class="product-placeholder">
                                    <i class="product-icon">🦻</i>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="related-product-content">
                            <h3 class="related-product-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h3>
                            
                            <div class="related-product-excerpt">
                                <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                            </div>
                            
                            <?php if ($related_price) : ?>
                                <div class="related-product-price"><?php echo esc_html($related_price); ?></div>
                            <?php endif; ?>
                            
                            <a href="<?php the_permalink(); ?>" class="btn btn-outline btn-sm">
                                <?php _e('View Details', 'volkena'); ?>
                            </a>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
    </section>
    <?php 
    wp_reset_postdata();
    endif; 
    ?>

    <!-- Contact Form Modal -->
    <div id="contact-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><?php _e('Request Information', 'volkena'); ?></h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p><?php printf(__('Get more information about %s', 'volkena'), '<strong>' . get_the_title() . '</strong>'); ?></p>
                <form id="product-inquiry-form" class="contact-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="inquiry-name"><?php _e('Name', 'volkena'); ?> *</label>
                            <input type="text" id="inquiry-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="inquiry-email"><?php _e('Email', 'volkena'); ?> *</label>
                            <input type="email" id="inquiry-email" name="email" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inquiry-phone"><?php _e('Phone', 'volkena'); ?></label>
                        <input type="tel" id="inquiry-phone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="inquiry-message"><?php _e('Message', 'volkena'); ?> *</label>
                        <textarea id="inquiry-message" name="message" rows="4" required placeholder="<?php _e('Please provide details about your hearing needs and any questions you have about this product.', 'volkena'); ?>"></textarea>
                    </div>
                    <input type="hidden" name="product" value="<?php echo esc_attr(get_the_title()); ?>">
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <?php _e('Send Inquiry', 'volkena'); ?>
                        </button>
                        <button type="button" class="btn btn-outline modal-close">
                            <?php _e('Cancel', 'volkena'); ?>
                        </button>
                    </div>
                </form>
                <div id="inquiry-message" class="form-message"></div>
            </div>
        </div>
    </div>

    <?php
        do_action('volkena_product_after');
        endwhile;
    ?>
</main>

<?php do_action('volkena_content_after'); ?>

<?php get_footer(); ?>
