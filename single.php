<?php
/**
 * The template for displaying single blog posts
 *
 * @package Volkena
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php while (have_posts()) : the_post(); ?>
    
    <!-- Breadcrumb -->
    <section class="breadcrumb-section" style="background: #f8f9fa; padding: 1rem 0;">
        <div class="container">
            <nav class="breadcrumb">
                <a href="<?php echo esc_url(home_url('/')); ?>"><?php _e('Home', 'volkena'); ?></a>
                <span class="separator">›</span>
                <?php if (get_option('page_for_posts')) : ?>
                    <a href="<?php echo esc_url(get_permalink(get_option('page_for_posts'))); ?>"><?php _e('Blog', 'volkena'); ?></a>
                <?php else : ?>
                    <a href="<?php echo esc_url(home_url('/blog/')); ?>"><?php _e('Blog', 'volkena'); ?></a>
                <?php endif; ?>
                <span class="separator">›</span>
                <span class="current"><?php the_title(); ?></span>
            </nav>
        </div>
    </section>

    <!-- Article Header -->
    <section class="article-header" style="background: white; padding: 3rem 0;">
        <div class="container">
            <div class="article-header-content">
                <div class="article-meta">
                    <time datetime="<?php echo get_the_date('c'); ?>" class="article-date">
                        <?php echo get_the_date(); ?>
                    </time>
                    <span class="meta-separator">•</span>
                    <span class="article-author">
                        <?php _e('by', 'volkena'); ?> <?php the_author(); ?>
                    </span>
                    <?php if (has_category()) : ?>
                        <span class="meta-separator">•</span>
                        <span class="article-category">
                            <?php the_category(', '); ?>
                        </span>
                    <?php endif; ?>
                </div>
                
                <h1 class="article-title"><?php the_title(); ?></h1>
                
                <?php if (has_excerpt()) : ?>
                    <div class="article-excerpt">
                        <?php the_excerpt(); ?>
                    </div>
                <?php endif; ?>
                
                <?php if (has_post_thumbnail()) : ?>
                    <div class="article-featured-image">
                        <?php the_post_thumbnail('large'); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Article Content -->
    <section class="section article-content">
        <div class="container">
            <div class="article-layout">
                <div class="article-main">
                    <div class="content-wrapper">
                        <?php the_content(); ?>
                        
                        <?php
                        wp_link_pages(array(
                            'before' => '<div class="page-links">' . __('Pages:', 'volkena'),
                            'after'  => '</div>',
                        ));
                        ?>
                    </div>
                    
                    <!-- Article Tags -->
                    <?php if (has_tag()) : ?>
                        <div class="article-tags">
                            <h3><?php _e('Tags', 'volkena'); ?></h3>
                            <div class="tag-list">
                                <?php the_tags('', '', ''); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Article Navigation -->
                    <div class="article-navigation">
                        <div class="nav-links">
                            <?php
                            $prev_post = get_previous_post();
                            $next_post = get_next_post();
                            ?>
                            
                            <?php if ($prev_post) : ?>
                                <div class="nav-previous">
                                    <span class="nav-label"><?php _e('Previous Article', 'volkena'); ?></span>
                                    <a href="<?php echo get_permalink($prev_post); ?>" class="nav-title">
                                        <?php echo get_the_title($prev_post); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($next_post) : ?>
                                <div class="nav-next">
                                    <span class="nav-label"><?php _e('Next Article', 'volkena'); ?></span>
                                    <a href="<?php echo get_permalink($next_post); ?>" class="nav-title">
                                        <?php echo get_the_title($next_post); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Comments -->
                    <?php if (comments_open() || get_comments_number()) : ?>
                        <div class="article-comments">
                            <?php comments_template(); ?>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Sidebar -->
                <aside class="article-sidebar">
                    <!-- Author Bio -->
                    <div class="sidebar-widget author-bio">
                        <h3><?php _e('About the Author', 'volkena'); ?></h3>
                        <div class="author-info">
                            <div class="author-avatar">
                                <?php echo get_avatar(get_the_author_meta('ID'), 80); ?>
                            </div>
                            <div class="author-details">
                                <h4><?php the_author(); ?></h4>
                                <?php if (get_the_author_meta('description')) : ?>
                                    <p><?php echo get_the_author_meta('description'); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Related Articles -->
                    <?php
                    $related_posts = new WP_Query(array(
                        'posts_per_page' => 3,
                        'post__not_in' => array(get_the_ID()),
                        'category__in' => wp_get_post_categories(get_the_ID()),
                        'orderby' => 'rand'
                    ));
                    
                    if ($related_posts->have_posts()) :
                    ?>
                        <div class="sidebar-widget related-articles">
                            <h3><?php _e('Related Articles', 'volkena'); ?></h3>
                            <div class="related-posts">
                                <?php while ($related_posts->have_posts()) : $related_posts->the_post(); ?>
                                    <article class="related-post">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <div class="related-post-image">
                                                <a href="<?php the_permalink(); ?>">
                                                    <?php the_post_thumbnail('thumbnail'); ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        <div class="related-post-content">
                                            <h4 class="related-post-title">
                                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                            </h4>
                                            <div class="related-post-meta">
                                                <time datetime="<?php echo get_the_date('c'); ?>">
                                                    <?php echo get_the_date(); ?>
                                                </time>
                                            </div>
                                        </div>
                                    </article>
                                <?php endwhile; ?>
                            </div>
                        </div>
                    <?php 
                    wp_reset_postdata();
                    endif; 
                    ?>
                    
                    <!-- Newsletter Signup -->
                    <div class="sidebar-widget newsletter-signup">
                        <h3><?php _e('Stay Informed', 'volkena'); ?></h3>
                        <p><?php _e('Subscribe to our newsletter for the latest hearing health tips and product updates.', 'volkena'); ?></p>
                        <form class="newsletter-form">
                            <div class="form-group">
                                <input type="email" placeholder="<?php _e('Your email address', 'volkena'); ?>" required>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                <?php _e('Subscribe', 'volkena'); ?>
                            </button>
                        </form>
                    </div>
                    
                    <!-- Contact CTA -->
                    <div class="sidebar-widget contact-cta">
                        <div class="cta-content">
                            <h3><?php _e('Need Hearing Help?', 'volkena'); ?></h3>
                            <p><?php _e('Our specialists are ready to help you find the perfect hearing solution.', 'volkena'); ?></p>
                            <a href="<?php echo esc_url(get_permalink(get_page_by_path('contact'))); ?>" class="btn btn-secondary btn-sm">
                                <?php _e('Contact Us', 'volkena'); ?>
                            </a>
                        </div>
                    </div>
                    
                    <!-- Sidebar Widgets -->
                    <?php if (is_active_sidebar('sidebar-1')) : ?>
                        <?php dynamic_sidebar('sidebar-1'); ?>
                    <?php endif; ?>
                </aside>
            </div>
        </div>
    </section>

    <!-- Related Products -->
    <?php
    $related_products = new WP_Query(array(
        'post_type' => 'product',
        'posts_per_page' => 3,
        'orderby' => 'rand'
    ));
    
    if ($related_products->have_posts()) :
    ?>
    <section class="section related-products-section" style="background: #f8f9fa;">
        <div class="container">
            <div class="section-title">
                <h2><?php _e('Explore Our Products', 'volkena'); ?></h2>
                <p><?php _e('Discover our range of premium hearing aids designed with German precision', 'volkena'); ?></p>
            </div>
            
            <div class="related-products-grid">
                <?php while ($related_products->have_posts()) : $related_products->the_post(); 
                    $product_type = get_post_meta(get_the_ID(), '_product_type', true);
                    $product_price = get_post_meta(get_the_ID(), '_product_price', true);
                ?>
                    <div class="product-card-small">
                        <div class="product-image-small">
                            <?php if (has_post_thumbnail()) : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium'); ?>
                                </a>
                            <?php else : ?>
                                <div class="product-placeholder-small">
                                    <i class="product-icon">🦻</i>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="product-content-small">
                            <h3 class="product-title-small">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h3>
                            
                            <?php if ($product_type) : ?>
                                <div class="product-type-small"><?php echo esc_html($product_type); ?></div>
                            <?php endif; ?>
                            
                            <?php if ($product_price) : ?>
                                <div class="product-price-small"><?php echo esc_html($product_price); ?></div>
                            <?php endif; ?>
                            
                            <a href="<?php the_permalink(); ?>" class="btn btn-outline btn-sm">
                                <?php _e('Learn More', 'volkena'); ?>
                            </a>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
    </section>
    <?php 
    wp_reset_postdata();
    endif; 
    ?>

    <?php endwhile; ?>
</main>

<?php get_footer(); ?>
