/*
Theme Name: <PERSON><PERSON>lkenahearing
Description: Professional WordPress theme for Völkenahearing company, inspired by Audien Hearing design. Features modern blue color scheme, custom typography, comprehensive hearing aid product showcase with Excel import functionality, and German precision branding.
Version: 2.0
Author: Völkenahearing Development Team
Text Domain: volkena
Domain Path: /languages
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Völkenahearing WordPress Theme, Copyright 2024 Völkenahearing Development Team
Völkenahearing is distributed under the terms of the GNU GPL

This theme is inspired by Audien Hearing's design principles while being customized for the Völkenahearing brand.
*/

/* CSS Custom Properties - Audien Inspired Color Scheme */
:root {
    --color-primary-1: 46,63,86;
    --color-primary-2: 255,255,255;
    --color-primary-3: 83,98,119;
    --color-primary-4: 137,159,189;
    --color-primary-5: 231,238,250;
    --color-primary-6: 255,250,221;
    --color-primary-7: 255,255,250;
    --color-primary-8: 85,85,85;
    --color-primary-9: 165,165,165;
    --color-primary-10: 230,230,230;
    --color-primary-11: 247,247,247;
    --color-secondary-1: 250,208,0;
    --color-secondary-2: 249,93,56;
    --color-secondary-3: 43,182,52;
    --color-secondary-4: 190,192,196;
    --color-secondary-5: 214,111,203;
    --color-gradient-1: linear-gradient(0deg, rgba(41, 56, 76, 1), rgba(79, 98, 124, 1) 100%);
    --color-gradient-2: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(231, 238, 250, 1) 100%);
    --color-gradient-3: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(228, 228, 228, 1) 100%);
    --color-gradient-4: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 250, 221, 1) 100%);

    /* Page Layout */
    --page-width: 1280px;
    --page-layout-padding: 20px;

    /* Typography */
    --font-primary: 'Garnett', 'Inter', 'Segoe UI', sans-serif;
    --font-secondary: 'Neuemontreal', 'Inter', 'Segoe UI', sans-serif;
}

/* Font Face Declarations - Audien Style Fonts */
@font-face {
    font-family: "Garnett";
    src: url("assets/fonts/Garnett-Light.woff2") format("woff2"),
         url("assets/fonts/Garnett-Light.woff") format("woff");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Garnett";
    src: url("assets/fonts/Garnett-Regular.woff2") format("woff2"),
         url("assets/fonts/Garnett-Regular.woff") format("woff");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Garnett";
    src: url("assets/fonts/Garnett-Medium.woff2") format("woff2"),
         url("assets/fonts/Garnett-Medium.woff") format("woff");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Garnett";
    src: url("assets/fonts/Garnett-Semibold.woff2") format("woff2"),
         url("assets/fonts/Garnett-Semibold.woff") format("woff");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Garnett";
    src: url("assets/fonts/Garnett-Bold.woff2") format("woff2"),
         url("assets/fonts/Garnett-Bold.woff") format("woff");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Neuemontreal";
    src: url("assets/fonts/NeueMontreal-Regular.woff2") format("woff2"),
         url("assets/fonts/NeueMontreal-Regular.woff") format("woff");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Neuemontreal";
    src: url("assets/fonts/NeueMontreal-Medium.woff2") format("woff2"),
         url("assets/fonts/NeueMontreal-Medium.woff") format("woff");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: rgb(var(--color-primary-8));
    background-color: rgb(var(--color-primary-2));
    font-size: 16px;
}

/* Typography - Audien Style */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: rgb(var(--color-primary-1));
}

h1 { font-size: 3rem; font-weight: 700; }
h2 { font-size: 2.5rem; font-weight: 600; }
h3 { font-size: 2rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 500; }
h5 { font-size: 1.25rem; font-weight: 500; }
h6 { font-size: 1.1rem; font-weight: 500; }

p {
    margin-bottom: 1rem;
    color: rgb(var(--color-primary-3));
    font-family: var(--font-secondary);
}

a {
    color: rgb(var(--color-primary-1));
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: rgba(var(--color-primary-1), 0.8);
}

/* Container and Layout - Audien Style */
.container {
    max-width: var(--page-width);
    margin: 0 auto;
    padding: 0 var(--page-layout-padding);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

/* Header Styles - Audien Inspired */
.site-header {
    background: rgb(var(--color-primary-2));
    box-shadow: 0 2px 20px rgba(var(--color-primary-1), 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(var(--color-primary-10), 0.5);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 0;
}

.site-logo {
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 700;
    color: rgb(var(--color-primary-1));
}

.site-logo span {
    color: rgb(var(--color-secondary-1));
}

/* Navigation - Audien Style */
.main-navigation {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
}

.nav-menu a {
    color: rgb(var(--color-primary-1));
    font-family: var(--font-secondary);
    font-weight: 500;
    font-size: 16px;
    padding: 0.5rem 0;
    position: relative;
    transition: all 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: rgb(var(--color-secondary-1));
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 0;
    height: 2px;
    background: rgb(var(--color-secondary-1));
    transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
    width: 100%;
}

/* Language Switcher */
.language-switcher {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.lang-btn {
    padding: 0.5rem 1rem;
    border: 2px solid rgb(var(--color-primary-1));
    background: transparent;
    color: rgb(var(--color-primary-1));
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-family: var(--font-secondary);
}

.lang-btn.active,
.lang-btn:hover {
    background: rgb(var(--color-primary-1));
    color: rgb(var(--color-primary-2));
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    background: transparent;
    border: none;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: rgb(var(--color-primary-1));
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section - Audien Style */
.hero-section {
    background: var(--color-gradient-2);
    padding: 140px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="%232e3f56" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: rgb(var(--color-primary-1));
    line-height: 1.1;
}

.hero-subtitle {
    font-family: var(--font-secondary);
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    color: rgb(var(--color-primary-3));
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons - Audien Style */
.btn {
    display: inline-block;
    padding: 16px 32px;
    border-radius: 8px;
    font-family: var(--font-secondary);
    font-weight: 600;
    font-size: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: rgb(var(--color-primary-1));
    color: rgb(var(--color-primary-2));
    border-color: rgb(var(--color-primary-1));
}

.btn-primary:hover {
    background: rgb(var(--color-primary-3));
    border-color: rgb(var(--color-primary-3));
    color: rgb(var(--color-primary-2));
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(var(--color-primary-1), 0.3);
}
}

.btn-secondary {
    background: rgb(var(--color-secondary-1));
    color: rgb(var(--color-primary-1));
    border-color: rgb(var(--color-secondary-1));
}

.btn-secondary:hover {
    background: rgba(var(--color-secondary-1), 0.9);
    border-color: rgba(var(--color-secondary-1), 0.9);
    color: rgb(var(--color-primary-1));
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(var(--color-secondary-1), 0.3);
}

.btn-outline {
    background: transparent;
    color: rgb(var(--color-primary-1));
    border: 2px solid rgb(var(--color-primary-1));
}

.btn-outline:hover {
    background: rgb(var(--color-primary-1));
    color: rgb(var(--color-primary-2));
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(var(--color-primary-1), 0.2);
}

/* Section Styles - Audien Inspired */
.section {
    padding: 100px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-title h2 {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: rgb(var(--color-primary-1));
}

.section-title p {
    font-family: var(--font-secondary);
    font-size: 1.2rem;
    color: rgb(var(--color-primary-3));
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Product Grid - Audien Style */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.product-card {
    background: rgb(var(--color-primary-2));
    border-radius: 12px;
    padding: 2.5rem;
    box-shadow: 0 10px 40px rgba(var(--color-primary-1), 0.08);
    transition: all 0.3s ease;
    text-align: center;
    border: 1px solid rgba(var(--color-primary-10), 0.5);
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(var(--color-primary-1), 0.15);
    border-color: rgba(var(--color-primary-4), 0.3);
}

.product-image {
    width: 100%;
    height: 220px;
    background: var(--color-gradient-2);
    border-radius: 8px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #3498db;
}

.product-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.product-description {
    color: #7f8c8d;
    margin-bottom: 1.5rem;
}

.product-features {
    list-style: none;
    margin-bottom: 2rem;
}

.product-features li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.product-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: rgb(var(--color-secondary-3));
    font-weight: bold;
}

/* Trust Indicators Section */
.trust-indicators {
    border-top: 1px solid rgba(var(--color-primary-10), 0.5);
    border-bottom: 1px solid rgba(var(--color-primary-10), 0.5);
}

.trust-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    text-align: center;
}

.trust-item {
    padding: 1.5rem;
}

.trust-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.trust-item h4 {
    font-family: var(--font-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: rgb(var(--color-primary-1));
}

.trust-item p {
    font-family: var(--font-secondary);
    color: rgb(var(--color-primary-3));
    font-size: 0.95rem;
    line-height: 1.4;
}

/* Product Showcase Section */
.product-showcase-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-showcase-card {
    background: rgb(var(--color-primary-2));
    border-radius: 12px;
    padding: 2.5rem;
    text-align: center;
    position: relative;
    border: 1px solid rgba(var(--color-primary-10), 0.3);
    transition: all 0.3s ease;
    box-shadow: 0 8px 30px rgba(var(--color-primary-1), 0.08);
}

.product-showcase-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 50px rgba(var(--color-primary-1), 0.15);
    border-color: rgba(var(--color-primary-4), 0.4);
}

.product-showcase-card.featured {
    border: 2px solid rgb(var(--color-secondary-1));
    transform: scale(1.05);
}

.product-showcase-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.product-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgb(var(--color-secondary-1));
    color: rgb(var(--color-primary-1));
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    font-family: var(--font-secondary);
}

.product-image-placeholder {
    width: 120px;
    height: 120px;
    background: var(--color-gradient-2);
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid rgba(var(--color-primary-4), 0.2);
}

.product-image-placeholder .product-icon {
    font-size: 3rem;
    color: rgb(var(--color-primary-1));
}

.product-showcase-card h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: rgb(var(--color-primary-1));
}

.product-type {
    font-family: var(--font-secondary);
    font-size: 0.9rem;
    color: rgb(var(--color-primary-3));
    margin-bottom: 1rem;
    font-weight: 500;
}

.product-description {
    font-family: var(--font-secondary);
    color: rgb(var(--color-primary-3));
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.product-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-tag {
    background: rgba(var(--color-primary-5), 0.8);
    color: rgb(var(--color-primary-1));
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    font-family: var(--font-secondary);
}

.product-price {
    margin-bottom: 2rem;
}

.price-from {
    display: block;
    font-family: var(--font-secondary);
    font-size: 0.9rem;
    color: rgb(var(--color-primary-3));
    margin-bottom: 0.25rem;
}

.price-amount {
    font-family: var(--font-primary);
    font-size: 2rem;
    font-weight: 700;
    color: rgb(var(--color-primary-1));
}

.btn-large {
    padding: 20px 40px;
    font-size: 1.1rem;
}

/* Footer - Audien Style */
.site-footer {
    background: rgb(var(--color-primary-1));
    color: rgb(var(--color-primary-2));
    padding: 4rem 0 2rem;
    position: relative;
}

.site-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--color-gradient-1);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    font-family: var(--font-primary);
    color: rgb(var(--color-primary-2));
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.footer-section p {
    color: rgba(var(--color-primary-2), 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-family: var(--font-secondary);
}

.footer-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-menu li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: rgba(var(--color-primary-2), 0.7);
    display: block;
    padding: 0.3rem 0;
    font-family: var(--font-secondary);
    transition: color 0.3s ease;
    text-decoration: none;
}

.footer-section a:hover {
    color: rgb(var(--color-secondary-1));
}

.footer-social {
    margin-bottom: 1.5rem;
}

.social-link {
    display: inline-block;
    margin-right: 1rem;
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.social-link:hover {
    transform: scale(1.2);
}

.footer-certifications {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.cert-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
}

.cert-icon {
    font-size: 1.2rem;
}

.cert-text {
    font-family: var(--font-secondary);
    font-size: 0.9rem;
    color: rgba(var(--color-primary-2), 0.8);
}

.footer-bottom {
    border-top: 1px solid rgba(var(--color-primary-2), 0.2);
    padding-top: 2rem;
    text-align: center;
    color: rgba(var(--color-primary-2), 0.6);
    font-family: var(--font-secondary);
}

/* Product Filters */
.product-filters {
    background: rgb(var(--color-primary-2));
    border-bottom: 1px solid rgba(var(--color-primary-10), 0.5);
}

.filters-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.filter-group label,
.sort-group label {
    font-family: var(--font-secondary);
    font-weight: 600;
    color: rgb(var(--color-primary-1));
    margin-right: 1rem;
    font-size: 0.95rem;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.product-filter-btn {
    background: transparent;
    border: 2px solid rgba(var(--color-primary-4), 0.3);
    color: rgb(var(--color-primary-3));
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-family: var(--font-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.product-filter-btn:hover,
.product-filter-btn.active {
    background: rgb(var(--color-primary-1));
    border-color: rgb(var(--color-primary-1));
    color: rgb(var(--color-primary-2));
}

.filter-tab {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    margin-right: 0.5rem;
    background: transparent;
    border: 2px solid rgba(var(--color-primary-4), 0.3);
    color: rgb(var(--color-primary-3));
    text-decoration: none;
    border-radius: 25px;
    font-family: var(--font-secondary);
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.filter-tab:hover,
.filter-tab.active {
    background: rgb(var(--color-primary-1));
    border-color: rgb(var(--color-primary-1));
    color: rgb(var(--color-primary-2));
}

#product-sort {
    padding: 0.5rem 1rem;
    border: 2px solid rgba(var(--color-primary-4), 0.3);
    border-radius: 5px;
    background: rgb(var(--color-primary-2));
    color: rgb(var(--color-primary-1));
    font-family: var(--font-secondary);
    cursor: pointer;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 2rem;
}

.page-header-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.page-title {
    font-family: var(--font-primary);
    font-size: 3.5rem;
    font-weight: 700;
    color: rgb(var(--color-primary-1));
    margin-bottom: 1.5rem;
}

.page-description {
    font-family: var(--font-secondary);
    font-size: 1.25rem;
    color: rgb(var(--color-primary-3));
    line-height: 1.6;
}

/* Responsive Design - Audien Style */
@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: 2rem;
    }

    .product-showcase-grid,
    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .trust-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .main-navigation {
        display: none;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .hero-subtitle,
    .page-description {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-secondary {
        margin-left: 0;
        margin-top: 1rem;
    }

    .product-grid,
    .product-showcase-grid,
    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .trust-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .section {
        padding: 60px 0;
    }

    .filters-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .filter-buttons {
        justify-content: flex-start;
    }

    .product-showcase-card.featured {
        transform: none;
    }

    .product-showcase-card.featured:hover {
        transform: translateY(-5px);
    }
}

@media (max-width: 480px) {
    .hero-title,
    .page-title {
        font-size: 2rem;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .btn {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

    .product-showcase-card,
    .product-card {
        padding: 1.5rem;
    }

    .container {
        padding: 0 15px;
    }
}

/* Enhanced Product Display Styles */
.product-model-no,
.product-no {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-family: var(--font-secondary);
}

.model-label,
.no-label {
    font-weight: 600;
    color: rgb(var(--color-primary-3));
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

.model-value,
.no-value {
    font-weight: 500;
    color: rgb(var(--color-primary-1));
    background: rgba(var(--color-secondary-1), 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

.product-price-large {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 700;
    color: rgb(var(--color-secondary-1));
    margin: 1rem 0;
}

.product-price-large .currency {
    font-size: 1.8rem;
    margin-right: 0.25rem;
}

.product-measurement {
    display: flex;
    align-items: center;
    margin-top: 0.75rem;
    font-family: var(--font-secondary);
}

.measurement-label {
    font-weight: 600;
    color: rgb(var(--color-primary-3));
    margin-right: 0.5rem;
    font-size: 0.9rem;
}

.measurement-value {
    font-weight: 500;
    color: rgb(var(--color-primary-1));
    background: rgba(var(--color-primary-4), 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

.product-accessories-section,
.product-features-section,
.product-packaging-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(var(--color-primary-5), 0.3);
    border-radius: 12px;
    border-left: 4px solid rgb(var(--color-secondary-1));
}

.product-accessories-section h3,
.product-features-section h3,
.product-packaging-section h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    color: rgb(var(--color-primary-1));
    margin-bottom: 1rem;
}

.product-accessories-list,
.product-features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.product-accessories-list li,
.product-features-list li {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    font-family: var(--font-secondary);
    color: rgb(var(--color-primary-3));
    border-bottom: 1px solid rgba(var(--color-primary-10), 0.5);
}

.product-accessories-list li:last-child,
.product-features-list li:last-child {
    border-bottom: none;
}

.accessory-icon,
.feature-icon {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.feature-icon {
    color: rgb(var(--color-secondary-3));
    font-weight: bold;
}

.packaging-info {
    display: flex;
    align-items: center;
    font-family: var(--font-secondary);
    color: rgb(var(--color-primary-3));
    font-size: 1.1rem;
}

.packaging-icon {
    margin-right: 0.75rem;
    font-size: 1.5rem;
}

.specifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgb(var(--color-primary-2));
    border-radius: 8px;
    border: 1px solid rgba(var(--color-primary-10), 0.5);
}

.spec-name {
    font-family: var(--font-secondary);
    font-weight: 600;
    color: rgb(var(--color-primary-1));
    flex: 1;
}

.spec-value {
    font-family: var(--font-secondary);
    font-weight: 500;
    color: rgb(var(--color-primary-3));
    text-align: right;
    flex: 1;
}

/* Features Section */
.features-section {
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    text-align: center;
    padding: 2rem 1rem;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* Product Types */
.product-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-type-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.product-type-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.product-type-image {
    margin-bottom: 1.5rem;
}

.product-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.product-type-card h3 {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.product-type-card h4 {
    color: #7f8c8d;
    font-weight: 500;
    margin-bottom: 1rem;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
}

.feature-list li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

/* About Preview */
.about-preview {
    padding: 80px 0;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
    text-align: left;
}

.service-features li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.service-features li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: #3498db;
    font-weight: bold;
}

/* Testimonials */
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content p {
    font-style: italic;
    color: #555;
    font-size: 1.1rem;
    line-height: 1.6;
}

.testimonial-author strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 0.25rem;
}

.testimonial-author span {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    text-align: center;
}

.cta-buttons {
    margin: 2rem 0;
}

.cta-buttons .btn {
    margin: 0 0.5rem;
}

/* Blog Preview */
.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.blog-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.blog-image {
    height: 200px;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-content {
    padding: 1.5rem;
}

.blog-meta {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.blog-title {
    margin-bottom: 1rem;
}

.blog-title a {
    color: #2c3e50;
    text-decoration: none;
}

.blog-title a:hover {
    color: #3498db;
}

.blog-excerpt {
    color: #555;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.read-more {
    color: #3498db;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: #2980b9;
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .product-types {
        grid-template-columns: 1fr;
    }

    .about-stats {
        justify-content: center;
        gap: 1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .blog-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .feature-card,
    .service-card,
    .testimonial-card {
        padding: 1.5rem;
    }
}

/* Product Archive Styles */
.page-header {
    text-align: center;
}

.page-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.page-description {
    font-size: 1.2rem;
    color: #7f8c8d;
    max-width: 600px;
    margin: 0 auto;
}

.product-filters {
    position: sticky;
    top: 80px;
    z-index: 100;
}

.filters-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-group,
.sort-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.filter-group label,
.sort-group label {
    font-weight: 600;
    color: #2c3e50;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.product-filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #ecf0f1;
    background: white;
    color: #2c3e50;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.product-filter-btn:hover,
.product-filter-btn.active {
    border-color: #3498db;
    background: #3498db;
    color: white;
}

#product-sort {
    padding: 0.5rem 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 5px;
    background: white;
    color: #2c3e50;
    cursor: pointer;
}

.product-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    background: #f8f9fa;
    border-radius: 10px;
    color: #bdc3c7;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #ecf0f1;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.more-features {
    color: #3498db;
    font-style: italic;
}

.no-products {
    text-align: center;
    padding: 4rem 0;
}

.no-products-content h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.no-products-content p {
    color: #7f8c8d;
    margin-bottom: 2rem;
}

/* Product Info Section */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.info-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.info-card h3 {
    color: #3498db;
    margin-bottom: 1rem;
}

.info-card ul {
    list-style: none;
    padding: 0;
    margin-top: 1rem;
}

.info-card ul li {
    padding: 0.25rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.info-card ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

/* Single Product Styles */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: #3498db;
    text-decoration: none;
}

.breadcrumb .separator {
    color: #bdc3c7;
}

.breadcrumb .current {
    color: #2c3e50;
    font-weight: 500;
}

.product-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 2rem;
}

.product-images {
    position: sticky;
    top: 100px;
    height: fit-content;
}

.main-image {
    margin-bottom: 1rem;
}

.product-main-image {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.product-placeholder-large {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    background: #f8f9fa;
    border-radius: 10px;
    color: #bdc3c7;
}

.product-placeholder-large .product-icon {
    font-size: 6rem;
    margin-bottom: 1rem;
}

.product-type-badge {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin: 1rem 0;
}

.product-price-large {
    font-size: 2rem;
    font-weight: 700;
    color: #27ae60;
    margin: 1rem 0;
}

.product-features-section {
    margin: 2rem 0;
}

.product-features-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.product-features-list {
    list-style: none;
    padding: 0;
}

.product-features-list li {
    padding: 0.75rem 0;
    color: #555;
    position: relative;
    padding-left: 2rem;
    border-bottom: 1px solid #ecf0f1;
}

.product-features-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
    font-size: 1.2rem;
}

.product-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
    text-align: center;
}

/* Benefits Section */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.benefit-card {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.benefit-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.benefit-card p {
    color: #7f8c8d;
}

/* Related Products */
.related-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.related-product-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.related-product-card:hover {
    transform: translateY(-5px);
}

.related-product-image {
    height: 200px;
    overflow: hidden;
}

.related-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-product-content {
    padding: 1.5rem;
}

.related-product-title a {
    color: #2c3e50;
    text-decoration: none;
}

.related-product-title a:hover {
    color: #3498db;
}

.related-product-excerpt {
    color: #7f8c8d;
    margin: 1rem 0;
}

.related-product-price {
    font-weight: 600;
    color: #27ae60;
    margin-bottom: 1rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #bdc3c7;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 1.5rem;
}

/* About Page Styles */
.about-hero-content {
    text-align: center;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.engineering-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.engineering-features {
    margin-top: 2rem;
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.feature-content h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: #7f8c8d;
    margin: 0;
}

.engineering-visual {
    text-align: center;
}

.visual-placeholder {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 3rem;
    border: 2px dashed #bdc3c7;
}

.visual-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.products-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-showcase-item {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.showcase-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.showcase-features {
    list-style: none;
    padding: 0;
    margin-top: 1.5rem;
    text-align: left;
}

.showcase-features li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.showcase-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #27ae60;
    font-weight: bold;
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.process-step {
    text-align: center;
    padding: 2rem;
}

.step-number {
    width: 60px;
    height: 60px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

.step-content h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.step-content p {
    color: #7f8c8d;
}

.commitment-points {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.commitment-point {
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.commitment-point h3 {
    color: #3498db;
    margin-bottom: 1rem;
}

.commitment-point p {
    color: #555;
    margin: 0;
}

/* Contact Page Styles */
.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-5px);
}

.contact-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.contact-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.contact-card p {
    color: #7f8c8d;
    margin: 0;
}

.contact-card a {
    color: #3498db;
    text-decoration: none;
}

.contact-card a:hover {
    color: #2980b9;
}

.contact-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-form-container {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-header {
    margin-bottom: 2rem;
}

.form-header h2 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #7f8c8d;
    margin: 0;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid #ecf0f1;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

.checkbox-group {
    flex-direction: row;
    align-items: flex-start;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    width: auto;
}

.form-actions {
    margin-top: 1rem;
}

.map-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.map-header {
    padding: 2rem;
    border-bottom: 1px solid #ecf0f1;
}

.map-header h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.map-header p {
    color: #7f8c8d;
    margin: 0;
}

.map-placeholder {
    padding: 3rem;
    text-align: center;
    background: #f8f9fa;
    border-bottom: 1px solid #ecf0f1;
}

.map-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.map-address {
    color: #7f8c8d;
    margin: 1rem 0;
}

.location-details {
    padding: 2rem;
}

.location-details h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.location-info {
    list-style: none;
    padding: 0;
    margin: 0;
}

.location-info li {
    padding: 0.5rem 0;
    color: #555;
    border-bottom: 1px solid #ecf0f1;
}

.location-info li:last-child {
    border-bottom: none;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.faq-item {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.faq-item h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.faq-item p {
    color: #7f8c8d;
    margin: 0;
    line-height: 1.6;
}

.quick-contact-content {
    text-align: center;
}

.quick-contact-options {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.quick-contact-btn {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255,255,255,0.1);
    color: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.quick-contact-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
    color: white;
}

.contact-text {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.contact-text strong {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.contact-text small {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Services Archive Styles */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card-large {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.service-card-large:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.service-image {
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.service-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    background: #f8f9fa;
    color: #bdc3c7;
}

.service-placeholder .service-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.service-content {
    padding: 2rem;
}

.service-title a {
    color: #2c3e50;
    text-decoration: none;
}

.service-title a:hover {
    color: #3498db;
}

.service-excerpt {
    color: #7f8c8d;
    margin: 1rem 0;
    line-height: 1.6;
}

.service-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.process-timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.timeline-step {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.step-icon {
    width: 60px;
    height: 60px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

.step-content h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.step-content p {
    color: #7f8c8d;
    margin: 0;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.benefit-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
}

.benefit-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.benefit-item h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.benefit-item p {
    color: #7f8c8d;
    margin: 0;
}

/* Blog Styles */
.article-header-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.article-meta {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.meta-separator {
    margin: 0 0.5rem;
}

.article-title {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.article-excerpt {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.article-featured-image {
    margin-top: 2rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.article-featured-image img {
    width: 100%;
    height: auto;
}

.article-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin-top: 2rem;
}

.content-wrapper {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
}

.content-wrapper h2,
.content-wrapper h3,
.content-wrapper h4 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.content-wrapper p {
    margin-bottom: 1.5rem;
}

.content-wrapper ul,
.content-wrapper ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.content-wrapper li {
    margin-bottom: 0.5rem;
}

.article-tags {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #ecf0f1;
}

.article-tags h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-list a {
    background: #ecf0f1;
    color: #2c3e50;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.tag-list a:hover {
    background: #3498db;
    color: white;
}

.article-navigation {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #ecf0f1;
}

.nav-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.nav-previous,
.nav-next {
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.nav-previous:hover,
.nav-next:hover {
    background: #ecf0f1;
}

.nav-next {
    text-align: right;
}

.nav-label {
    display: block;
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 0.5rem;
}

.nav-title {
    color: #2c3e50;
    text-decoration: none;
    font-weight: 600;
}

.nav-title:hover {
    color: #3498db;
}

.article-sidebar {
    position: sticky;
    top: 100px;
    height: fit-content;
}

.sidebar-widget {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.sidebar-widget h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.author-info {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.author-avatar {
    flex-shrink: 0;
}

.author-avatar img {
    border-radius: 50%;
}

.author-details h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.author-details p {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin: 0;
}

.related-posts {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.related-post {
    display: flex;
    gap: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.related-post:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.related-post-image {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    border-radius: 5px;
    overflow: hidden;
}

.related-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-post-content {
    flex: 1;
}

.related-post-title {
    margin-bottom: 0.5rem;
}

.related-post-title a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 0.9rem;
    line-height: 1.3;
}

.related-post-title a:hover {
    color: #3498db;
}

.related-post-meta {
    color: #7f8c8d;
    font-size: 0.8rem;
}

.newsletter-signup {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.newsletter-signup h3 {
    color: white;
}

.newsletter-signup p {
    color: rgba(255,255,255,0.9);
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.newsletter-form input {
    padding: 0.75rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
}

.contact-cta {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    text-align: center;
}

.contact-cta h3 {
    color: white;
}

.contact-cta p {
    color: rgba(255,255,255,0.9);
    margin-bottom: 1.5rem;
}

.product-card-small {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.product-card-small:hover {
    transform: translateY(-5px);
}

.product-image-small {
    height: 150px;
    overflow: hidden;
}

.product-image-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-placeholder-small {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 150px;
    background: #f8f9fa;
    color: #bdc3c7;
}

.product-placeholder-small .product-icon {
    font-size: 2rem;
}

.product-content-small {
    padding: 1.5rem;
    text-align: center;
}

.product-title-small {
    margin-bottom: 0.5rem;
}

.product-title-small a {
    color: #2c3e50;
    text-decoration: none;
    font-size: 1rem;
}

.product-title-small a:hover {
    color: #3498db;
}

.product-type-small {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.product-price-small {
    color: #27ae60;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Responsive Design Enhancements */
@media (max-width: 1024px) {
    .product-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .product-images {
        position: static;
    }

    .engineering-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .article-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .article-sidebar {
        position: static;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .article-title {
        font-size: 2rem;
    }

    .filters-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .filter-buttons {
        flex-wrap: wrap;
    }

    .product-actions {
        flex-direction: column;
    }

    .btn-large {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .process-timeline {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .contact-info-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .quick-contact-options {
        flex-direction: column;
        align-items: center;
    }

    .quick-contact-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .nav-links {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .nav-next {
        text-align: left;
    }

    .author-info {
        flex-direction: column;
        text-align: center;
    }

    .related-post {
        flex-direction: column;
        text-align: center;
    }

    .related-post-image {
        width: 100%;
        height: 150px;
        align-self: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .article-title {
        font-size: 1.75rem;
    }

    .section {
        padding: 60px 0;
    }

    .product-grid {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .contact-info-grid {
        grid-template-columns: 1fr;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .commitment-points {
        grid-template-columns: 1fr;
    }

    .products-showcase {
        grid-template-columns: 1fr;
    }

    .process-steps {
        grid-template-columns: 1fr;
    }

    .sidebar-widget {
        padding: 1.5rem;
    }

    .contact-card,
    .service-card,
    .testimonial-card,
    .benefit-card,
    .feature-card {
        padding: 1.5rem;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .modal-header,
    .modal-body {
        padding: 1rem;
    }
}

/* Print Styles */
@media print {
    .site-header,
    .site-footer,
    .article-sidebar,
    .whatsapp-float,
    .back-to-top,
    .compare-bar {
        display: none !important;
    }

    .article-main {
        width: 100% !important;
    }

    .container {
        max-width: none !important;
        padding: 0 !important;
    }

    .section {
        padding: 20px 0 !important;
    }

    body {
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000 !important;
        background: #fff !important;
    }

    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        page-break-after: avoid;
    }

    p, li {
        orphans: 3;
        widows: 3;
    }

    img {
        max-width: 100% !important;
        height: auto !important;
    }

    .btn {
        display: none !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        color: #fff;
        border: 2px solid #000;
    }

    .btn-outline {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }

    .product-card,
    .service-card,
    .testimonial-card {
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .hero-section::before {
        animation: none !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1a1a1a;
        --text-color: #e0e0e0;
        --card-bg: #2d2d2d;
        --border-color: #404040;
    }

    body {
        background-color: var(--bg-color);
        color: var(--text-color);
    }

    .product-card,
    .service-card,
    .testimonial-card,
    .sidebar-widget {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
    }

    .site-header {
        background: var(--card-bg);
        border-bottom: 1px solid var(--border-color);
    }

    .hero-section,
    .section {
        background: var(--bg-color);
    }

    h1, h2, h3, h4, h5, h6 {
        color: var(--text-color);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        background: var(--card-bg);
        color: var(--text-color);
        border-color: var(--border-color);
    }
}
