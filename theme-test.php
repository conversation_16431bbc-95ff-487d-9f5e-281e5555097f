<?php
/**
 * Theme Test File - Verify Völkena Theme Functionality
 * 
 * This file can be used to test if the theme is working correctly.
 * Access it by going to: yoursite.com/wp-content/themes/volkena/theme-test.php
 * 
 * @package Volkena
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    require_once('../../../wp-load.php');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Völkena Theme Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #3498db;
        }
        .test-header h1 {
            color: #3498db;
            margin-bottom: 10px;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status.success {
            background: #27ae60;
            color: white;
        }
        .status.error {
            background: #e74c3c;
            color: white;
        }
        .status.warning {
            background: #f39c12;
            color: white;
        }
        .test-item {
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .footer-note {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🎯 Völkena Theme Test</h1>
            <p>Comprehensive theme functionality verification</p>
        </div>

        <div class="test-section">
            <h3>📁 Theme Files</h3>
            <?php
            $required_files = [
                'style.css' => 'Main stylesheet',
                'index.php' => 'Main template',
                'functions.php' => 'Theme functions',
                'header.php' => 'Header template',
                'footer.php' => 'Footer template',
                'front-page.php' => 'Homepage template',
                'single-product.php' => 'Product template',
                'archive-product.php' => 'Product archive',
            ];

            foreach ($required_files as $file => $description) {
                $exists = file_exists($file);
                echo '<div class="test-item">';
                echo '<span>' . $description . ' (' . $file . ')</span>';
                echo '<span class="status ' . ($exists ? 'success' : 'error') . '">';
                echo $exists ? 'Found' : 'Missing';
                echo '</span>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>🔧 Theme Constants</h3>
            <?php
            $constants = [
                'VOLKENA_VERSION' => 'Theme version',
                'VOLKENA_THEME_DIR' => 'Theme directory',
                'VOLKENA_THEME_URI' => 'Theme URI',
            ];

            foreach ($constants as $constant => $description) {
                $defined = defined($constant);
                echo '<div class="test-item">';
                echo '<span>' . $description . ' (' . $constant . ')</span>';
                echo '<span class="status ' . ($defined ? 'success' : 'error') . '">';
                echo $defined ? 'Defined' : 'Not Defined';
                echo '</span>';
                echo '</div>';
                
                if ($defined) {
                    echo '<div class="test-item" style="margin-left: 20px; font-size: 12px; color: #7f8c8d;">';
                    echo '<span>Value: ' . constant($constant) . '</span>';
                    echo '</div>';
                }
            }
            ?>
        </div>

        <div class="test-section">
            <h3>📦 WordPress Functions</h3>
            <?php
            $wp_functions = [
                'wp_enqueue_style' => 'Style enqueuing',
                'wp_enqueue_script' => 'Script enqueuing',
                'add_theme_support' => 'Theme support',
                'register_nav_menus' => 'Navigation menus',
                'add_action' => 'Action hooks',
                'add_filter' => 'Filter hooks',
            ];

            foreach ($wp_functions as $function => $description) {
                $exists = function_exists($function);
                echo '<div class="test-item">';
                echo '<span>' . $description . ' (' . $function . ')</span>';
                echo '<span class="status ' . ($exists ? 'success' : 'error') . '">';
                echo $exists ? 'Available' : 'Missing';
                echo '</span>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>🎨 Asset Files</h3>
            <?php
            $asset_files = [
                'assets/css/elementor.css' => 'Elementor styles',
                'assets/css/blocks.css' => 'Gutenberg blocks styles',
                'assets/css/editor-style.css' => 'Editor styles',
                'assets/js/main.js' => 'Main JavaScript',
                'assets/js/blocks.js' => 'Gutenberg blocks',
                'assets/js/frontend-editor.js' => 'Frontend editor',
                'assets/js/customizer-preview.js' => 'Customizer preview',
            ];

            foreach ($asset_files as $file => $description) {
                $exists = file_exists($file);
                echo '<div class="test-item">';
                echo '<span>' . $description . ' (' . $file . ')</span>';
                echo '<span class="status ' . ($exists ? 'success' : 'error') . '">';
                echo $exists ? 'Found' : 'Missing';
                echo '</span>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>🌐 Language Files</h3>
            <?php
            $language_files = [
                'languages/de_DE.po' => 'German translation',
                'languages/volkena.pot' => 'Translation template',
            ];

            foreach ($language_files as $file => $description) {
                $exists = file_exists($file);
                echo '<div class="test-item">';
                echo '<span>' . $description . ' (' . $file . ')</span>';
                echo '<span class="status ' . ($exists ? 'success' : 'error') . '">';
                echo $exists ? 'Found' : 'Missing';
                echo '</span>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>📋 Theme Information</h3>
            <?php
            if (function_exists('wp_get_theme')) {
                $theme = wp_get_theme();
                echo '<div class="test-item">';
                echo '<span>Theme Name</span>';
                echo '<span>' . $theme->get('Name') . '</span>';
                echo '</div>';
                
                echo '<div class="test-item">';
                echo '<span>Version</span>';
                echo '<span>' . $theme->get('Version') . '</span>';
                echo '</div>';
                
                echo '<div class="test-item">';
                echo '<span>Description</span>';
                echo '<span>' . substr($theme->get('Description'), 0, 100) . '...</span>';
                echo '</div>';
            } else {
                echo '<div class="test-item">';
                echo '<span>WordPress not fully loaded</span>';
                echo '<span class="status warning">Warning</span>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>✅ Test Summary</h3>
            <?php
            $total_tests = 0;
            $passed_tests = 0;
            
            // Count file tests
            foreach ($required_files as $file => $description) {
                $total_tests++;
                if (file_exists($file)) $passed_tests++;
            }
            
            // Count constant tests
            foreach ($constants as $constant => $description) {
                $total_tests++;
                if (defined($constant)) $passed_tests++;
            }
            
            // Count function tests
            foreach ($wp_functions as $function => $description) {
                $total_tests++;
                if (function_exists($function)) $passed_tests++;
            }
            
            // Count asset tests
            foreach ($asset_files as $file => $description) {
                $total_tests++;
                if (file_exists($file)) $passed_tests++;
            }
            
            $success_rate = round(($passed_tests / $total_tests) * 100, 1);
            
            echo '<div class="test-item">';
            echo '<span><strong>Tests Passed</strong></span>';
            echo '<span><strong>' . $passed_tests . ' / ' . $total_tests . ' (' . $success_rate . '%)</strong></span>';
            echo '</div>';
            
            if ($success_rate >= 90) {
                echo '<div class="test-item">';
                echo '<span><strong>Overall Status</strong></span>';
                echo '<span class="status success">Excellent</span>';
                echo '</div>';
            } elseif ($success_rate >= 75) {
                echo '<div class="test-item">';
                echo '<span><strong>Overall Status</strong></span>';
                echo '<span class="status warning">Good</span>';
                echo '</div>';
            } else {
                echo '<div class="test-item">';
                echo '<span><strong>Overall Status</strong></span>';
                echo '<span class="status error">Needs Attention</span>';
                echo '</div>';
            }
            ?>
        </div>

        <div class="footer-note">
            <p><strong>Völkena WordPress Theme</strong> - German Precision Hearing Solutions</p>
            <p>Test completed on <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
</body>
</html>
